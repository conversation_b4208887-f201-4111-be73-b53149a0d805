# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/panpan/code/Calib/CALIB_t11_ceju

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/panpan/code/Calib/CALIB_t11_ceju/build_0626

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/panpan/code/Calib/CALIB_t11_ceju/build_0626/CMakeFiles /home/<USER>/panpan/code/Calib/CALIB_t11_ceju/build_0626//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/panpan/code/Calib/CALIB_t11_ceju/build_0626/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named testOpencv_vslam2

# Build rule for target.
testOpencv_vslam2: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 testOpencv_vslam2
.PHONY : testOpencv_vslam2

# fast build rule for target.
testOpencv_vslam2/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/build
.PHONY : testOpencv_vslam2/fast

MSRCR.o: MSRCR.cpp.o
.PHONY : MSRCR.o

# target to build an object file
MSRCR.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/MSRCR.cpp.o
.PHONY : MSRCR.cpp.o

MSRCR.i: MSRCR.cpp.i
.PHONY : MSRCR.i

# target to preprocess a source file
MSRCR.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/MSRCR.cpp.i
.PHONY : MSRCR.cpp.i

MSRCR.s: MSRCR.cpp.s
.PHONY : MSRCR.s

# target to generate assembly for a file
MSRCR.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/MSRCR.cpp.s
.PHONY : MSRCR.cpp.s

calibration.o: calibration.cpp.o
.PHONY : calibration.o

# target to build an object file
calibration.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/calibration.cpp.o
.PHONY : calibration.cpp.o

calibration.i: calibration.cpp.i
.PHONY : calibration.i

# target to preprocess a source file
calibration.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/calibration.cpp.i
.PHONY : calibration.cpp.i

calibration.s: calibration.cpp.s
.PHONY : calibration.s

# target to generate assembly for a file
calibration.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/calibration.cpp.s
.PHONY : calibration.cpp.s

forwardCalib.o: forwardCalib.cpp.o
.PHONY : forwardCalib.o

# target to build an object file
forwardCalib.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/forwardCalib.cpp.o
.PHONY : forwardCalib.cpp.o

forwardCalib.i: forwardCalib.cpp.i
.PHONY : forwardCalib.i

# target to preprocess a source file
forwardCalib.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/forwardCalib.cpp.i
.PHONY : forwardCalib.cpp.i

forwardCalib.s: forwardCalib.cpp.s
.PHONY : forwardCalib.s

# target to generate assembly for a file
forwardCalib.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/forwardCalib.cpp.s
.PHONY : forwardCalib.cpp.s

getCalibData.o: getCalibData.cpp.o
.PHONY : getCalibData.o

# target to build an object file
getCalibData.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/getCalibData.cpp.o
.PHONY : getCalibData.cpp.o

getCalibData.i: getCalibData.cpp.i
.PHONY : getCalibData.i

# target to preprocess a source file
getCalibData.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/getCalibData.cpp.i
.PHONY : getCalibData.cpp.i

getCalibData.s: getCalibData.cpp.s
.PHONY : getCalibData.s

# target to generate assembly for a file
getCalibData.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/getCalibData.cpp.s
.PHONY : getCalibData.cpp.s

main.o: main.cpp.o
.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/main.cpp.s
.PHONY : main.cpp.s

mapComputer.o: mapComputer.cpp.o
.PHONY : mapComputer.o

# target to build an object file
mapComputer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/mapComputer.cpp.o
.PHONY : mapComputer.cpp.o

mapComputer.i: mapComputer.cpp.i
.PHONY : mapComputer.i

# target to preprocess a source file
mapComputer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/mapComputer.cpp.i
.PHONY : mapComputer.cpp.i

mapComputer.s: mapComputer.cpp.s
.PHONY : mapComputer.s

# target to generate assembly for a file
mapComputer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/testOpencv_vslam2.dir/build.make CMakeFiles/testOpencv_vslam2.dir/mapComputer.cpp.s
.PHONY : mapComputer.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... testOpencv_vslam2"
	@echo "... MSRCR.o"
	@echo "... MSRCR.i"
	@echo "... MSRCR.s"
	@echo "... calibration.o"
	@echo "... calibration.i"
	@echo "... calibration.s"
	@echo "... forwardCalib.o"
	@echo "... forwardCalib.i"
	@echo "... forwardCalib.s"
	@echo "... getCalibData.o"
	@echo "... getCalibData.i"
	@echo "... getCalibData.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... mapComputer.o"
	@echo "... mapComputer.i"
	@echo "... mapComputer.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

