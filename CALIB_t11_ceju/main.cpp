#include "calibration.h"
#include "forwardCalib.h"
#include "mapComputer.h"
#include <opencv2/opencv.hpp>
#include <fstream>
#include <yaml-cpp/yaml.h>
#include <sys/stat.h>

using namespace std;
using namespace cv;

int main() {
    try {
        YAML::Node config = YAML::LoadFile("../config/config_0626.yaml");
        YAML::Node intrix = YAML::LoadFile("../config/calib_intrix_0612.yaml");


        // 读取路径和相机参数
        const std::string imgPath = config["paths"]["input_image"].as<std::string>();
        const std::string savePath = config["paths"]["save_path"].as<std::string>();
        const auto paraCamVec = intrix["camera_parameters"]["paraCam"].as<std::vector<double>>();

        // 创建保存路径（如不存在）
        struct stat info;
        if (stat(savePath.c_str(), &info) != 0) {
            std::cout << "创建保存路径: " << savePath << std::endl;
            if (mkdir(savePath.c_str(), 0777) != 0) {
                std::cerr << "❌ 创建目录失败: " << savePath << std::endl;
                return 1;
            }
        }

        // 图像读取
        cv::Mat image = cv::imread(imgPath, cv::IMREAD_COLOR);
        if (image.empty()) {
            std::cerr << "❌ 无法读取图像: " << imgPath << std::endl;
            return -1;
        }
        std::cout << "✅ 成功读取图像: " << imgPath << "，尺寸: " << image.cols << "x" << image.rows << std::endl;

        // 标定
        double paraCam[12];
        std::copy(paraCamVec.begin(), paraCamVec.end(), paraCam);
        int result = getCalibData(imgPath.c_str(), paraCam, savePath.c_str(), config);
        if (result != 0) {
            std::cerr << "❌ 标定失败，错误码: " << result << std::endl;
            return result;
        }
        std::cout << "✅ 标定成功完成！" << std::endl;
        return 0;
        
    } catch (const YAML::Exception& e) {
        std::cerr << "❌ YAML解析错误: " << e.what() << std::endl;
        return -1;
    }
}


