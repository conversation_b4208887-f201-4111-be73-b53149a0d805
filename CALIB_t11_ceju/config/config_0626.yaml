## main.cpp
paths:
  input_image: "/home/<USER>/panpan/code/Calib/CALIB_t12_ceju/data/input/imgs_1280x720_0626/20250626_111241.bmp" ## test图片
  save_path: "output/" ## 保存前视标定图片

## forwardCalib.cpp
image_dimensions:
  src_height: 720 ## 原图高
  src_width: 1280 ## 原图宽
  out_height: 720 ## 输出高
  out_width: 1280 ## 输出宽

chessboard_corners:
  rows: 11      # 内部角点行数（注意不是格子数）
  cols: 8      # 内部角点列数

chessboard_bounds: ## 原图标定板的左上第一个角点和右下角点,修改数值保证尽量不要有黑边
  x_min: 530 ## 547-10
  y_min: 274 ## 247-5
  x_max: 740 ## 663+10
  y_max: 415 ## 324+5

bottom_Threshold: 20 ## 从图像底部往上裁切多少像素值
chessboard_y_offset: 37 ## 棋盘格最后一个角点向下扩展的像素值

## getCalibData.cpp
world_limits:
  x_min: 0 ## 物理距离最小值（z方向）
  x_max: 75 ## 物理距离最大值（z方向）
  y_min: -125 
  y_max: 125 
 

image_range:
  imgx_range: [30, 1250] ## 图像中 X（列）方向选取从像素 30 到 1250 的区域
  imgy_range: [450, 700] ## 图像中 Y（行）方向选取从像素 450 到 700 的区域

dark_thresh: 220 ##小于这个值的区域认为是"黑色区域",用于加深黑色区域（让它变得更黑，但不小于0）

blob_detector:
  filter_by_color: true ## （按颜色过滤）
  blob_color: 0 ## 只检测黑色 Blob（灰度值为 0）

  filter_by_area: true ## （按面积过滤） 只检测面积在 200 到 100000 像素之间的斑点
  min_area: 200
  max_area: 100000  

  filter_by_circularity: true ## （按圆度过滤）
  min_circularity: 0.05
  max_circularity: 0.99

  filter_by_convexity: false ## （按凸性过滤）凸性是轮廓面积与其凸包面积的比值，排除边缘有缺口的 blob
  min_convexity: 0.87
  max_convexity: 1.0

  filter_by_inertia: false ## （按惯性比过滤）用来剔除细长形状或近似正圆形
  min_inertia_ratio: 0.05
  max_inertia_ratio: 0.99

column_point_counts: ## 左右列黑子个数
  left:  [6, 6, 5, 4, 4, 3, 2]
  right: [6, 6, 5, 4, 4, 3, 2]


## Calibration.cpp
word_grid_info:
  cols: 14  ## 14列黑子
  rows: 7 ## 6行黑子加上相机位置所在行
  dis_to_camera: 1 ## 相机到光心的距离 （z方向）
  dis_to_camera2center: 0  ##相机距离中心线的偏移 （横向）
  h_axis: [75, 57, 45, 35, 25, 15, 0] ## 0为相机位置，数值代表z方向距相机距离，单位cm
  w_axis: [-32.5, -27.5, -22.5, -17.5, -12.5, -7.5, -2.5, 2.5, 7.5, 12.5, 17.5, 22.5, 27.5, 32.5] ## 横向离相机距离，相机位于中间，设置左负右正，也可以自己定义

