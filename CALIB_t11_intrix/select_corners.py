import cv2
import numpy as np

def select_points_from_image(image_path, window_name='Select Corners', num_points=2):
    """
    在图像上用鼠标选择点，返回所选点列表。

    Args:
        image_path (str): 图像文件路径
        window_name (str): 显示窗口名称
        num_points (int): 需要选择的点数，默认2个

    Returns:
        list of tuple: 所选点坐标列表 [(x1, y1), (x2, y2), ...]
    """
    img = cv2.imread(image_path)
    if img is None:
        raise FileNotFoundError(f"无法读取图片: {image_path}")

    clone = img.copy()
    points = []

    def mouse_callback(event, x, y, flags, param):
        if event == cv2.EVENT_LBUTTONDOWN:
            if len(points) < num_points:
                points.append((x, y))
                cv2.circle(img, (x, y), 3, (0, 255, 0), -1)
                cv2.imshow(window_name, img)
                print(f"Selected point: ({x}, {y})")

    cv2.namedWindow(window_name)
    cv2.setMouseCallback(window_name, mouse_callback)
    cv2.imshow(window_name, img)
    print(f"请依次点击 {num_points} 个点，完成后按 'q' 退出。")

    while True:
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q') or len(points) == num_points:
            break

    cv2.destroyAllWindows()
    return points

if __name__ == "__main__":
    image_path = '/home/<USER>/panpan/code/Calib/CALIB_t12_ceju/data/input/20250626-175342.bmp'
    selected_points = select_points_from_image(image_path)
    if len(selected_points) >= 2:
        print("\nSelected points:")
        print(f"Top-left: {selected_points[0]}")
        print(f"Bottom-right: {selected_points[1]}")
    else:
        print("未选择足够的点。")
