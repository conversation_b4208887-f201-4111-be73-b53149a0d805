# Pixel to Physical Distance Converter

This Python project converts pixel coordinates to physical distances using a calibration table. It's a Python implementation of the original C++ code.

## Requirements

- Python 3.7+
- numpy
- pyyaml

## Installation

1. Create a virtual environment (recommended):
```bash
python -m venv .venv
source .venv/bin/activate  # On Linux/Mac
# or
.venv\Scripts\activate  # On Windows
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Project Structure

- `pixel_converter.py`: Contains the `PixelConverter` class for converting pixel coordinates to physical coordinates
- `detection_processor.py`: Contains the `DetectionProcessor` class for processing detection results
- `main.py`: Example usage of the code
- `requirements.txt`: Project dependencies

## Usage

1. Make sure you have the following files in the correct locations:
   - `size_ranges.yaml`: Configuration file for object size ranges
   - `distance_table`: Binary file containing the calibration data

2. Run the example:
```bash
python main.py
```

## Configuration

The `size_ranges.yaml` file should have the following structure:
```yaml
objects:
  bin:
    max_size: 100.0
    min_size: 10.0
    description: "Bin size is out of expected range"
  # Add more objects as needed
default:
  max_size: 50.0
  min_size: 5.0
  description: "Object size is out of expected range"
```

## Notes

- The distance table file should be a binary file containing the calibration data
- The code assumes certain image dimensions and coordinate ranges, which can be adjusted in the `PixelConverter` class
- Error handling is implemented throughout the code to provide meaningful error messages 