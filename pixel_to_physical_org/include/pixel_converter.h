#ifndef PIXEL_CONVERTER_H
#define PIXEL_CONVERTER_H

#include <string>
#include <iostream>
#include <fstream>
#include <vector>
#include <utility>
#include <random>
#include <chrono>

// 图像像素范围常量
const int IMGX_RANGE[2] = {30, 1250};  // x range of pixels to calibrate location (cols)
const int IMGY_RANGE[2] = {400, 720};  // y range of pixels to calibrate location (rows)

// 物理坐标范围常量
const double X_MIN = 0;
const double X_MAX = 82;
const double Y_MIN = -125;
const double Y_MAX = 125;

// 计算表格尺寸
const int TABLE_WIDTH = IMGX_RANGE[1] - IMGX_RANGE[0] + 1;
const int TABLE_HEIGHT = IMGY_RANGE[1] - IMGY_RANGE[0] + 1;

class PixelConverter {
public:
    // 构造函数，初始化distance_table文件路径
    explicit PixelConverter(const std::string& table_path);
    
    // 查询物理位置
    bool queryPhysicalLocation(int pixel_x, int pixel_y, double& physical_x, double& physical_y);
    
    // // 批量查询物理位置
    // std::vector<std::pair<double, double>> batchQuery(const std::vector<std::pair<int, int>>& pixel_points);
    
    // // 生成随机测试点
    // std::vector<std::pair<int, int>> generateRandomPoints(int num_points);
    
    // 验证像素坐标是否在有效范围内
    bool isValidPixelCoordinate(int pixel_x, int pixel_y) const;

private:
    std::string table_path_;
    
    // 将0-255的值映射回实际物理范围
    static double mapValueBack(uint8_t value, double min_val, double max_val);
    
    // 读取distance_table文件中的值
    bool readTableValues(int pixel_x, int pixel_y, uint8_t& x_value, uint8_t& y_value);
};

#endif // PIXEL_CONVERTER_H 