#ifndef DETECTION_PROCESSOR_H
#define DETECTION_PROCESSOR_H

#include <string>
#include <utility>
#include "../include/pixel_converter.h"
#include <yaml-cpp/yaml.h>

#ifdef __cplusplus
extern "C" {
#endif

// 检测框结构体
typedef struct {
    int x1, y1;  // 左上角
    int x2, y2;  // 右下角
} BBox;

// 检测结果结构体
typedef struct {
    BBox bbox;
    char label[32];  // 使用固定大小的字符数组存储标签
    float confidence;
    double left_distance;
    double right_distance;
    double physical_distance;
} DetectionResult;

class DetectionProcessor {
    private:
        YAML::Node size_ranges_config;
        void loadSizeRangesConfig();
        bool isSizeReasonableForLabel(const char* label, double max_size, double min_size);

    public:
        DetectionProcessor() = default;
        void setSizeRangesConfig(const YAML::Node& config) { size_ranges_config = config; }
        static bool calculateTargetSize(const BBox& bbox, PixelConverter& converter,
                                    double& size_y, double& size_x, double& x1_out, double& x2_out);
        bool isDetectionReasonable(DetectionResult* det, const char* table_path, double& out_x1, double& out_x2);
        void processDetectionResult(DetectionResult* det, const char* table_path);
};

#ifdef __cplusplus
}
#endif

#endif // DETECTION_PROCESSOR_H 