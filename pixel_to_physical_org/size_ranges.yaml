# 物体尺寸范围配置（单位：厘米）
objects:
  seatbase:
    max_size: 120
    min_size: 30
    description: "座椅底座通常在 50~100cm"
  
  scales:
    max_size: 50
    min_size: 10
    description: "电子秤常见尺寸约 20~40cm"
  
  bin:
    max_size: 60
    min_size: 20
    description: "普通垃圾桶高度约 40~60cm"
  
  cloth:
    max_size: 150
    min_size: 30
    description: "衣物尺寸变化较大，通常在 30~150cm"
  
  rug:
    max_size: 300
    min_size: 50
    description: "地毯长度通常在 100~300cm"
  
  shoe:
    max_size: 35
    min_size: 5
    description: "鞋类尺寸变化较大，通常最大不超过35cm"
  
  wire:
    max_size: 200
    min_size: 1
    description: "电线的常见尺寸从 1cm 到 200cm"
  
  rail:
    max_size: 300
    min_size: 20
    description: "轨道长度一般在 100~300cm"
  
  wheel:
    max_size: 100
    min_size: 10
    description: "车轮直径常见尺寸在 20~100cm"
  
  shit:
    max_size: 10
    min_size: 2
    description: "排泄物通常尺寸较小，约 2~10cm"
  
  sandbasin:
    max_size: 100
    min_size: 20
    description: "沙盆尺寸通常在 30~100cm"
  
  bei_bowl:
    max_size: 30
    min_size: 5
    description: "贝壳碗的常见尺寸大约为 5~30cm"
  
  wan_bowl:
    max_size: 50
    min_size: 10
    description: "碗的常见尺寸通常在 10~50cm"
  
  big_shack:
    max_size: 200
    min_size: 100
    description: "大型棚屋尺寸大约在 150~200cm"
  
  sml_shack:
    max_size: 100
    min_size: 50
    description: "小型棚屋尺寸通常在 50~100cm"

# 默认尺寸范围（用于未知类别）
default:
  max_size: 150
  min_size: 2
  description: "未知类别的默认尺寸范围" 