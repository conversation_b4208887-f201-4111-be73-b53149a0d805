#include "../include/pixel_converter.h"
#include <iomanip>
#include <cmath>

// 检测结果结构体
struct DetectionResult {
    struct BBox {
        int x1, y1;  // 左上角
        int x2, y2;  // 右下角
    } bbox;
    std::string label;
    float confidence;
    double physical_distance;  // 新增：实际物理距离
};

// 打印物理坐标的辅助函数
void printPhysicalLocation(const std::pair<int, int>& pixel, const std::pair<double, double>& physical) {
    std::cout << std::fixed << std::setprecision(3);
    std::cout << "像素坐标 (" << std::setw(4) << pixel.first << ", " << std::setw(4) << pixel.second 
              << ") -> 物理位置 (" << std::setw(6) << physical.first << ", " << std::setw(6) << physical.second << ")" << std::endl;
}

// 计算目标尺寸的函数
std::pair<int, int> calculateBoxSize(const DetectionResult::BBox& bbox) {
    int width = bbox.x2 - bbox.x1;
    int height = bbox.y2 - bbox.y1;
    return {width, height};
}

// 计算物理距离和尺寸的辅助函数
DetectionResult calculatePhysicalProperties(DetectionResult* det, PixelConverter& converter) {
    // 获取左下角和右下角的物理坐标
    double x1, y1, x2, y2;
    if (converter.queryPhysicalLocation(det->bbox.x1, det->bbox.y2, x1, y1) && 
        converter.queryPhysicalLocation(det->bbox.x2, det->bbox.y2, x2, y2)) {
        
        // 打印左下角和右下角的物理坐标
        printPhysicalLocation({det->bbox.x1, det->bbox.y2}, {x1, y1});
        printPhysicalLocation({det->bbox.x2, det->bbox.y2}, {x2, y2});
        
        
        // 更新检测结果的物理距离和目标尺寸
        det->physical_distance = (x1 + x2) / 2;  // 使用中心点物理位置作为实际物理距离
        std::cout << "实际物理距离: " << std::fixed << std::setprecision(3) << det->physical_distance << " cm" << std::endl;
    } else {
        std::cout << "无法转换坐标" << std::endl;
    }
    return *det;
}

// 打印检测结果的函数
void printDetectionDetails(const DetectionResult* det) {
    std::cout << "\n处理检测结果：" << std::endl;
    std::cout << "标签: " << det->label << ", 置信度: " << det->confidence << std::endl;
    std::cout << "----------------------------------------" << std::endl;
}

// 判断检测结果是否合理的函数
bool isDetectionReasonable(const DetectionResult* det, PixelConverter& converter) {
    if (det == nullptr) {
        std::cerr << "检测结果为空!" << std::endl;
        return false;
    }

    // 1. 检查检测框是否在图像范围内
    if (!converter.isValidPixelCoordinate(det->bbox.x1, det->bbox.y1) ||
        !converter.isValidPixelCoordinate(det->bbox.x2, det->bbox.y2)) {
        std::cout << "警告：检测框超出图像范围" << std::endl;
        return false;
    }

    // // 2. 检查检测框的宽高比是否合理
    // double aspect_ratio = static_cast<double>(det->bbox.x2 - det->bbox.x1) / 
    //                      static_cast<double>(det->bbox.y2 - det->bbox.y1);
    // if (aspect_ratio > 5.0 || aspect_ratio < 0.2) {
    //     std::cout << "警告：检测框宽高比异常 (" << aspect_ratio << ")" << std::endl;
    //     return false;
    // }

    // 3. 检查检测框是否太小（可能是误检）
    int box_area = (det->bbox.x2 - det->bbox.x1) * (det->bbox.y2 - det->bbox.y1);
    if (box_area < 10) {  // 小于100像素
        std::cout << "警告：检测框面积过小 (" << box_area << " 像素)" << std::endl;
        return false;
    }

    // 4. 检查检测框是否在扫地机器人可到达的范围内
    double x1, y1, x2, y2;
    if (converter.queryPhysicalLocation(det->bbox.x1, det->bbox.y2, x1, y1) && 
        converter.queryPhysicalLocation(det->bbox.x2, det->bbox.y2, x2, y2)) {
        // 检查物理距离是否在合理范围内（例如：扫地机器人最大工作范围）
        double distance = std::abs(x2 - x1);
        if (distance > 200) {  // 假设最大工作范围为2米
            std::cout << "警告：目标距离过远 (" << distance << " mm)" << std::endl;
            return false;
        }
    }
    // 5. 根据标签进行特定判断
    // 计算目标尺寸（左下角和右下角物理坐标的差值）
    double size_y = std::abs(y2 - y1);
    double size_x = std::abs(x2 - x1);
    double max_size = std::max(size_x, size_y);
    std::cout << "目标尺寸: " << std::fixed << std::setprecision(3) << max_size << " cm" << std::endl;
    if (max_size > 200) {
        std::cout << "警告：目标尺寸超过 200 cm" << std::endl;
        return false;
    }
    // 5. 根据标签进行特定判断
    if (det->label == "shoes") {
       
        // 根据标签进行逻辑判断
        if (max_size > 50) {
            std::cout << "警告：鞋子尺寸超过 50 cm" << std::endl;
            return false;
        }
       
    } else if (det->label == "trash") {
         if (max_size > 30) {
            std::cout << "警告：垃圾尺寸超过 30 cm" << std::endl;
            return false;
        }
    }

    return true;
}

// 处理检测结果的主函数
void processDetectionResult(DetectionResult* det, PixelConverter& converter) {
    
    printDetectionDetails(det);

    // 首先判断检测结果是否合理
    if (!isDetectionReasonable(det, converter)) {
        std::cout << "检测结果不合理，跳过处理" << std::endl;
        return;
    }

    // 如果检测结果合理，继续处理
    calculatePhysicalProperties(det, converter);
}


int main() {
    // 创建转换器实例
    PixelConverter converter("distance_table");
    
    // 示例检测结果（没有物理距离）
    DetectionResult det = {
        {590, 481,  // 左上角
         1020, 532}, // 右下角
        "trash",   // 标签
        0.95       // 置信度
    };
    
    // 处理检测结果，通过指针修改结构体
    processDetectionResult(&det, converter);
    std::cout << "det.physical_distance = " << det.physical_distance << std::endl;
    return 0;
}
