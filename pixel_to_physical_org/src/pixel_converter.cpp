#include "../include/pixel_converter.h"

PixelConverter::PixelConverter(const std::string& table_path)
    : table_path_(table_path) {}

bool PixelConverter::isValidPixelCoordinate(int pixel_x, int pixel_y) const {
    return pixel_x >= IMGX_RANGE[0] && pixel_x <= IMGX_RANGE[1] &&
           pixel_y >= IMGY_RANGE[0] && pixel_y <= IMGY_RANGE[1];
}

double PixelConverter::mapValueBack(uint8_t value, double min_val, double max_val) {
    return min_val + (value / 255.0) * (max_val - min_val);
}

bool PixelConverter::readTableValues(int pixel_x, int pixel_y, uint8_t& x_value, uint8_t& y_value) {
    std::ifstream table_file(table_path_, std::ios::binary);
    if (!table_file.is_open()) {
        std::cerr << "Error: Cannot open distance_table file: " << table_path_ << std::endl;
        return false;
    }

    // 获取文件大小
    table_file.seekg(0, std::ios::end);
    size_t file_size = table_file.tellg();
    table_file.seekg(0, std::ios::beg);

    // 检查文件大小是否正确
    size_t expected_size = 2 * TABLE_WIDTH * TABLE_HEIGHT;
    if (file_size != expected_size) {
        std::cerr << "Error: Invalid file size!" << std::endl;
        std::cerr << "Expected size: " << expected_size << " bytes" << std::endl;
        std::cerr << "Actual size: " << file_size << " bytes" << std::endl;
        table_file.close();
        return false;
    }

    // 计算在文件中的偏移量
    int row_offset = pixel_y - IMGY_RANGE[0];
    int col_offset = pixel_x - IMGX_RANGE[0];
    int byte_offset = 2 * (row_offset * TABLE_WIDTH + col_offset);
    // 检查偏移量是否有效
    if (byte_offset < 0 || byte_offset >= file_size) {
        std::cerr << "Error: Invalid byte offset!" << std::endl;
        std::cerr << "Calculated offset: " << byte_offset << std::endl;
        std::cerr << "File size: " << file_size << std::endl;
        table_file.close();
        return false;
    }

    // 移动到对应位置并读取值
    table_file.seekg(byte_offset, std::ios::beg);
    if (table_file.fail()) {
        std::cerr << "Error: Failed to seek to position " << byte_offset << std::endl;
        table_file.close();
        return false;
    }
    

    table_file.read(reinterpret_cast<char*>(&x_value), 1);
    table_file.read(reinterpret_cast<char*>(&y_value), 1);

    std::cout << "从distance_table读取的原始值: x_value = " << static_cast<int>(x_value) << ", y_value = " << static_cast<int>(y_value) << std::endl;

    if (table_file.fail() || table_file.gcount() != 1) {
        std::cerr << "Error: Failed to read values at offset " << byte_offset << std::endl;
        table_file.close();
        return false;
    }

    table_file.close();
    return true;
}

bool PixelConverter::queryPhysicalLocation(int pixel_x, int pixel_y, double& physical_x, double& physical_y) {
    // 检查输入像素坐标是否在有效范围内
    if (!isValidPixelCoordinate(pixel_x, pixel_y)) {
        std::cerr << "Error: Pixel coordinates out of range!" << std::endl;
        std::cerr << "Valid range: X[" << IMGX_RANGE[0] << "," << IMGX_RANGE[1] 
                  << "], Y[" << IMGY_RANGE[0] << "," << IMGY_RANGE[1] << "]" << std::endl;
        return false;
    }

    // 读取表格值
    uint8_t x_value, y_value;
    if (!readTableValues(pixel_x, pixel_y, x_value, y_value)) {
        return false;
    }
    // 检查读取的值是否有效
    if (x_value == 0 && y_value == 0) {
        std::cerr << "Warning: Coordinates (0,0) detected at pixel position (" 
                  << pixel_x << "," << pixel_y << ")" << std::endl;
        std::cerr << "This might indicate an invalid or uncalibrated region." << std::endl;
    }

    // 将0-255的值映射回实际物理范围
    physical_x = mapValueBack(x_value, X_MIN, X_MAX);
    physical_y = mapValueBack(y_value, Y_MIN, Y_MAX);

    return true;
}

// std::vector<std::pair<double, double>> PixelConverter::batchQuery(
//     const std::vector<std::pair<int, int>>& pixel_points) {
    
//     std::vector<std::pair<double, double>> physical_points;
//     physical_points.reserve(pixel_points.size());

//     for (const auto& point : pixel_points) {
//         double x, y;
//         if (queryPhysicalLocation(point.first, point.second, x, y)) {
//             physical_points.emplace_back(x, y);
//         }
//     }

//     return physical_points;
// }

// std::vector<std::pair<int, int>> PixelConverter::generateRandomPoints(int num_points) {
//     std::vector<std::pair<int, int>> points;
//     points.reserve(num_points);
    
//     // 使用当前时间作为随机数种子
//     unsigned seed = std::chrono::system_clock::now().time_since_epoch().count();
//     std::mt19937 generator(seed);
    
//     // 创建均匀分布
//     std::uniform_int_distribution<int> x_dist(IMGX_RANGE[0], IMGX_RANGE[1]);
//     std::uniform_int_distribution<int> y_dist(IMGY_RANGE[0], IMGY_RANGE[1]);
    
//     // 生成随机点
//     for (int i = 0; i < num_points; ++i) {
//         points.emplace_back(x_dist(generator), y_dist(generator));
//     }
    
//     return points;
// } 