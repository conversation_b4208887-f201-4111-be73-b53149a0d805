// #include <detection_processor/detection_processor.h>
#include <../include/detection_processor.h>
#include <iostream>
#include <yaml-cpp/yaml.h>

int main() {
    try {
        // 加载配置文件
        YAML::Node config = YAML::LoadFile("../size_ranges.yaml");
        
        // 创建检测处理器实例
        DetectionProcessor processor;
        processor.setSizeRangesConfig(config);

        // 创建检测结果
        DetectionResult det = {
            // {130, 414,  // 左上角
            //  603, 429}, // 右下角
            {209, 480,  // 左上角
             1020, 550}, // 右下角
            "bin",    // 标签
            0.95,       // 置信度
        };

        // 处理检测结果
        processor.processDetectionResult(&det, "../distance_table");
        
        std::cout << "最终物理距离: " << det.physical_distance << " cm" << std::endl;
        std::cout << "左下角距离: " << det.left_distance << " cm" << std::endl;
        std::cout << "右下角距离: " << det.right_distance << " cm" << std::endl;


        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
} 