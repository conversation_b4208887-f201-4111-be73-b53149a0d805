#include "../include/detection_processor.h"
#include "../include/pixel_converter.h"
#include <iostream>
#include <iomanip>
#include <cmath>
#include <cstring>
#include <yaml-cpp/yaml.h>

// 打印物理坐标的辅助函数
static void printPhysicalLocation(const std::pair<int, int>& pixel, const std::pair<double, double>& physical) {
    std::cout << std::fixed << std::setprecision(3);
    std::cout << "像素坐标 (" << std::setw(4) << pixel.first << ", " << std::setw(4) << pixel.second 
              << ") -> 物理位置 (" << std::setw(6) << physical.first << ", " << std::setw(6) << physical.second << ")" << std::endl;
}

// 打印检测结果的函数
static void printDetectionDetails(const DetectionResult* det) {
    std::cout << "\n处理检测结果：" << std::endl;
    std::cout << "标签: " << det->label << ", 置信度: " << det->confidence << std::endl;
    std::cout << "----------------------------------------" << std::endl;
}

// 打印目标尺寸的函数
static void printTargetSize(double size_y, double size_x) {
    std::cout << "目标尺寸:" << std::endl;
    std::cout << "长度: " << std::fixed << std::setprecision(3) << size_y << " cm" << std::endl;
    std::cout << "宽度: " << std::fixed << std::setprecision(3) << size_x << " cm" << std::endl;
}

// 检查检测框是否在图像范围内
static bool isBoxInImageRange(const BBox& bbox, PixelConverter& converter) {
    if (!converter.isValidPixelCoordinate(bbox.x1, bbox.y1) ||
        !converter.isValidPixelCoordinate(bbox.x2, bbox.y2)) {
        std::cout << "警告：检测框超出图像范围" << std::endl;
        return false;
    }
    return true;
}

// 检查检测框是否太小
static bool isBoxTooSmall(const BBox& bbox) {
    int box_area = (bbox.x2 - bbox.x1) * (bbox.y2 - bbox.y1);
    if (box_area < 10) {
        std::cout << "警告：检测框面积过小 (" << box_area << " 像素)" << std::endl;
        return true;
    }
    return false;
}

// 检查目标距离是否合理
static bool isDistanceReasonable(double distance) {
    if (distance > X_MAX) {
        std::cout << "警告：目标距离过远 (" << distance << " cm)" << std::endl;
        return false;
    }
    return true;
}

bool DetectionProcessor::isSizeReasonableForLabel(const char* label, double max_size, double min_size) {
    try {
        YAML::Node object_config;
        if (size_ranges_config["objects"][label]) {
            object_config = size_ranges_config["objects"][label];
        } else {
            object_config = size_ranges_config["default"];
        }

        double config_max_size = object_config["max_size"].as<double>();
        double config_min_size = object_config["min_size"].as<double>();
        std::string description = object_config["description"].as<std::string>();

        if (max_size > config_max_size || min_size < config_min_size) {
            std::cout << "警告：" << description << std::endl;
            return false;
        }
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error checking size ranges: " << e.what() << std::endl;
        return false;
    }
}

bool DetectionProcessor::calculateTargetSize(const BBox& bbox, PixelConverter& converter,
                                          double& size_y, double& size_x, double& z1_out, double& z2_out) {
    double x1, y1, x2, y2, x3, y3, x4, y4;
    if (converter.queryPhysicalLocation(bbox.x1, bbox.y2, x1, y1) && 
        converter.queryPhysicalLocation(bbox.x2, bbox.y2, x2, y2) &&
        converter.queryPhysicalLocation(bbox.x1, bbox.y1, x3, y3) ) {

        // 打印四个角点的物理坐标
        printPhysicalLocation({bbox.x1, bbox.y2}, {x1, y1});
        printPhysicalLocation({bbox.x2, bbox.y2}, {x2, y2});
        printPhysicalLocation({bbox.x1, bbox.y1}, {x3, y3});
        // printPhysicalLocation({bbox.x2, bbox.y1}, {x4, y4});
        
        // 计算上下两边的长度
        double bottom_length = std::sqrt(std::pow(x2 - x1, 2) + std::pow(y2 - y1, 2));
        
        // 计算左右两边的长度
        double left_length = std::sqrt(std::pow(x3 - x1, 2) + std::pow(y3 - y1, 2));

        size_y = bottom_length;
        size_x = left_length; 
        
        // 打印各边的长度，用于调试
        std::cout << "----------------------------------------" << std::endl;
        std::cout << "下边长度: " << std::fixed << std::setprecision(3) << bottom_length << " cm" << std::endl;
        std::cout << "左边宽度: " << std::fixed << std::setprecision(3) << left_length << " cm" << std::endl;
        std::cout << "----------------------------------------" << std::endl;

        z1_out = x1;
        z2_out = x2;
        return true;
    }
    return false;
}

bool DetectionProcessor::isDetectionReasonable(DetectionResult* det, const char* table_path, double& out_z1, double& out_z2) {
    if (det == nullptr) {
        std::cerr << "检测结果为空!" << std::endl;
        return false;
    }

    PixelConverter converter(table_path);

    // 1. 检查检测框是否在图像范围内
    if (!isBoxInImageRange(det->bbox, converter)) {
        return false;
    }

    // 2. 检查检测框是否太小
    if (isBoxTooSmall(det->bbox)) {
        return false;
    }

    // 3. 计算目标尺寸并检查
    double size_y, size_x;
    if (!calculateTargetSize(det->bbox, converter, size_y, size_x, out_z1, out_z2)) {
        std::cout << "无法计算目标尺寸" << std::endl;
        return false;
    }

     // 4. 检查物理距离是否在合理范围内
    double distance = std::abs(size_x);
    if (!isDistanceReasonable(distance)) {
        return false;
    }

    // 打印目标尺寸
    printTargetSize(size_y, size_x);

    // 5. 根据标签进行特定判断
    double max_size = std::max(size_x, size_y);
    double min_size = std::min(size_x, size_y);
    return isSizeReasonableForLabel(det->label, max_size, min_size);
}

void DetectionProcessor::processDetectionResult(DetectionResult* det, const char* table_path) {
    double z1, z2;
    // 首先判断检测结果是否合理
    if (!isDetectionReasonable(det, table_path, z1, z2)) {
        std::cout << "检测结果不合理，跳过处理" << std::endl;
        return;
    }
    printDetectionDetails(det);
    // 更新检测结果的物理距离
    det->left_distance = z1;
    det->right_distance = z2;
    det->physical_distance = (z1 + z2) / 2;  // 使用左下角和右下角物理位置z平均值作为实际物理距离
    // std::cout << "实际物理距离: " << std::fixed << std::setprecision(3) << det->physical_distance << " cm" << std::endl;
} 