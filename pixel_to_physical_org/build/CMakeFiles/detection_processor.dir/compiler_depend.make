# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o: ../src/detection_processor.cpp \
  /usr/include/stdc-predef.h \
  ../include/detection_processor.h \
  /usr/include/c++/11/string \
  /usr/include/x86_64-linux-gnu/c++/11/bits/c++config.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/os_defines.h \
  /usr/include/features.h \
  /usr/include/features-time64.h \
  /usr/include/x86_64-linux-gnu/bits/wordsize.h \
  /usr/include/x86_64-linux-gnu/bits/timesize.h \
  /usr/include/x86_64-linux-gnu/sys/cdefs.h \
  /usr/include/x86_64-linux-gnu/bits/long-double.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/cpu_defines.h \
  /usr/include/c++/11/bits/stringfwd.h \
  /usr/include/c++/11/bits/memoryfwd.h \
  /usr/include/c++/11/bits/char_traits.h \
  /usr/include/c++/11/bits/stl_algobase.h \
  /usr/include/c++/11/bits/functexcept.h \
  /usr/include/c++/11/bits/exception_defines.h \
  /usr/include/c++/11/bits/cpp_type_traits.h \
  /usr/include/c++/11/ext/type_traits.h \
  /usr/include/c++/11/ext/numeric_traits.h \
  /usr/include/c++/11/bits/stl_pair.h \
  /usr/include/c++/11/bits/move.h \
  /usr/include/c++/11/type_traits \
  /usr/include/c++/11/bits/stl_iterator_base_types.h \
  /usr/include/c++/11/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/11/bits/concept_check.h \
  /usr/include/c++/11/debug/assertions.h \
  /usr/include/c++/11/bits/stl_iterator.h \
  /usr/include/c++/11/bits/ptr_traits.h \
  /usr/include/c++/11/debug/debug.h \
  /usr/include/c++/11/bits/predefined_ops.h \
  /usr/include/c++/11/bits/postypes.h \
  /usr/include/c++/11/cwchar \
  /usr/include/wchar.h \
  /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
  /usr/include/x86_64-linux-gnu/bits/floatn.h \
  /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
  /usr/lib/gcc/x86_64-linux-gnu/11/include/stddef.h \
  /usr/lib/gcc/x86_64-linux-gnu/11/include/stdarg.h \
  /usr/include/x86_64-linux-gnu/bits/wchar.h \
  /usr/include/x86_64-linux-gnu/bits/types/wint_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
  /usr/include/c++/11/cstdint \
  /usr/lib/gcc/x86_64-linux-gnu/11/include/stdint.h \
  /usr/include/stdint.h \
  /usr/include/x86_64-linux-gnu/bits/types.h \
  /usr/include/x86_64-linux-gnu/bits/typesizes.h \
  /usr/include/x86_64-linux-gnu/bits/time64.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-intn.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
  /usr/include/c++/11/bits/allocator.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/c++allocator.h \
  /usr/include/c++/11/ext/new_allocator.h \
  /usr/include/c++/11/new \
  /usr/include/c++/11/bits/exception.h \
  /usr/include/c++/11/bits/localefwd.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/c++locale.h \
  /usr/include/c++/11/clocale \
  /usr/include/locale.h \
  /usr/include/x86_64-linux-gnu/bits/locale.h \
  /usr/include/c++/11/iosfwd \
  /usr/include/c++/11/cctype \
  /usr/include/ctype.h \
  /usr/include/x86_64-linux-gnu/bits/endian.h \
  /usr/include/x86_64-linux-gnu/bits/endianness.h \
  /usr/include/c++/11/bits/ostream_insert.h \
  /usr/include/c++/11/bits/cxxabi_forced.h \
  /usr/include/c++/11/bits/stl_function.h \
  /usr/include/c++/11/backward/binders.h \
  /usr/include/c++/11/bits/range_access.h \
  /usr/include/c++/11/initializer_list \
  /usr/include/c++/11/bits/basic_string.h \
  /usr/include/c++/11/ext/atomicity.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/gthr.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/gthr-default.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
  /usr/include/x86_64-linux-gnu/bits/sched.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h \
  /usr/include/x86_64-linux-gnu/bits/cpu-set.h \
  /usr/include/time.h \
  /usr/include/x86_64-linux-gnu/bits/time.h \
  /usr/include/x86_64-linux-gnu/bits/timex.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
  /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
  /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
  /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
  /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
  /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
  /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h \
  /usr/include/x86_64-linux-gnu/bits/setjmp.h \
  /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h \
  /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/atomic_word.h \
  /usr/include/x86_64-linux-gnu/sys/single_threaded.h \
  /usr/include/c++/11/ext/alloc_traits.h \
  /usr/include/c++/11/bits/alloc_traits.h \
  /usr/include/c++/11/bits/stl_construct.h \
  /usr/include/c++/11/ext/string_conversions.h \
  /usr/include/c++/11/cstdlib \
  /usr/include/stdlib.h \
  /usr/include/x86_64-linux-gnu/bits/waitflags.h \
  /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
  /usr/include/x86_64-linux-gnu/sys/types.h \
  /usr/include/endian.h \
  /usr/include/x86_64-linux-gnu/bits/byteswap.h \
  /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
  /usr/include/x86_64-linux-gnu/sys/select.h \
  /usr/include/x86_64-linux-gnu/bits/select.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
  /usr/include/alloca.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
  /usr/include/c++/11/bits/std_abs.h \
  /usr/include/c++/11/cstdio \
  /usr/include/stdio.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
  /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
  /usr/include/c++/11/cerrno \
  /usr/include/errno.h \
  /usr/include/x86_64-linux-gnu/bits/errno.h \
  /usr/include/linux/errno.h \
  /usr/include/x86_64-linux-gnu/asm/errno.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/x86_64-linux-gnu/bits/types/error_t.h \
  /usr/include/c++/11/bits/charconv.h \
  /usr/include/c++/11/bits/functional_hash.h \
  /usr/include/c++/11/bits/hash_bytes.h \
  /usr/include/c++/11/bits/basic_string.tcc \
  /usr/include/c++/11/utility \
  /usr/include/c++/11/bits/stl_relops.h \
  ../include/pixel_converter.h \
  /usr/include/c++/11/iostream \
  /usr/include/c++/11/ostream \
  /usr/include/c++/11/ios \
  /usr/include/c++/11/exception \
  /usr/include/c++/11/bits/exception_ptr.h \
  /usr/include/c++/11/bits/cxxabi_init_exception.h \
  /usr/include/c++/11/typeinfo \
  /usr/include/c++/11/bits/nested_exception.h \
  /usr/include/c++/11/bits/ios_base.h \
  /usr/include/c++/11/bits/locale_classes.h \
  /usr/include/c++/11/bits/locale_classes.tcc \
  /usr/include/c++/11/system_error \
  /usr/include/x86_64-linux-gnu/c++/11/bits/error_constants.h \
  /usr/include/c++/11/stdexcept \
  /usr/include/c++/11/streambuf \
  /usr/include/c++/11/bits/streambuf.tcc \
  /usr/include/c++/11/bits/basic_ios.h \
  /usr/include/c++/11/bits/locale_facets.h \
  /usr/include/c++/11/cwctype \
  /usr/include/wctype.h \
  /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/ctype_base.h \
  /usr/include/c++/11/bits/streambuf_iterator.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/ctype_inline.h \
  /usr/include/c++/11/bits/locale_facets.tcc \
  /usr/include/c++/11/bits/basic_ios.tcc \
  /usr/include/c++/11/bits/ostream.tcc \
  /usr/include/c++/11/istream \
  /usr/include/c++/11/bits/istream.tcc \
  /usr/include/c++/11/fstream \
  /usr/include/c++/11/bits/codecvt.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/basic_file.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/c++io.h \
  /usr/include/c++/11/bits/fstream.tcc \
  /usr/include/c++/11/vector \
  /usr/include/c++/11/bits/stl_uninitialized.h \
  /usr/include/c++/11/bits/stl_vector.h \
  /usr/include/c++/11/bits/stl_bvector.h \
  /usr/include/c++/11/bits/vector.tcc \
  /usr/include/c++/11/random \
  /usr/include/c++/11/cmath \
  /usr/include/math.h \
  /usr/include/x86_64-linux-gnu/bits/math-vector.h \
  /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
  /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
  /usr/include/x86_64-linux-gnu/bits/fp-logb.h \
  /usr/include/x86_64-linux-gnu/bits/fp-fast.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h \
  /usr/include/x86_64-linux-gnu/bits/iscanonical.h \
  /usr/include/c++/11/limits \
  /usr/include/c++/11/bits/random.h \
  /usr/include/c++/11/bits/uniform_int_dist.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/opt_random.h \
  /usr/include/c++/11/bits/random.tcc \
  /usr/include/c++/11/numeric \
  /usr/include/c++/11/bits/stl_numeric.h \
  /usr/include/c++/11/bit \
  /usr/include/c++/11/chrono \
  /usr/include/c++/11/ratio \
  /usr/include/c++/11/ctime \
  /usr/include/c++/11/bits/parse_numbers.h \
  /usr/include/yaml-cpp/yaml.h \
  /usr/include/yaml-cpp/parser.h \
  /usr/include/c++/11/memory \
  /usr/include/c++/11/bits/stl_tempbuf.h \
  /usr/include/c++/11/bits/stl_raw_storage_iter.h \
  /usr/include/c++/11/bits/align.h \
  /usr/include/c++/11/bits/uses_allocator.h \
  /usr/include/c++/11/bits/unique_ptr.h \
  /usr/include/c++/11/tuple \
  /usr/include/c++/11/array \
  /usr/include/c++/11/bits/invoke.h \
  /usr/include/c++/11/bits/shared_ptr.h \
  /usr/include/c++/11/bits/shared_ptr_base.h \
  /usr/include/c++/11/bits/allocated_ptr.h \
  /usr/include/c++/11/bits/refwrap.h \
  /usr/include/c++/11/ext/aligned_buffer.h \
  /usr/include/c++/11/ext/concurrence.h \
  /usr/include/c++/11/bits/shared_ptr_atomic.h \
  /usr/include/c++/11/bits/atomic_base.h \
  /usr/include/c++/11/bits/atomic_lockfree_defines.h \
  /usr/include/c++/11/backward/auto_ptr.h \
  /usr/include/yaml-cpp/dll.h \
  /usr/include/yaml-cpp/emitter.h \
  /usr/include/c++/11/cstddef \
  /usr/include/c++/11/sstream \
  /usr/include/c++/11/bits/sstream.tcc \
  /usr/include/yaml-cpp/binary.h \
  /usr/include/yaml-cpp/emitterdef.h \
  /usr/include/yaml-cpp/emittermanip.h \
  /usr/include/yaml-cpp/null.h \
  /usr/include/yaml-cpp/ostream_wrapper.h \
  /usr/include/yaml-cpp/emitterstyle.h \
  /usr/include/yaml-cpp/stlemitter.h \
  /usr/include/c++/11/list \
  /usr/include/c++/11/bits/stl_list.h \
  /usr/include/c++/11/bits/list.tcc \
  /usr/include/c++/11/set \
  /usr/include/c++/11/bits/stl_tree.h \
  /usr/include/c++/11/bits/stl_set.h \
  /usr/include/c++/11/bits/stl_multiset.h \
  /usr/include/c++/11/bits/erase_if.h \
  /usr/include/c++/11/map \
  /usr/include/c++/11/bits/stl_map.h \
  /usr/include/c++/11/bits/stl_multimap.h \
  /usr/include/yaml-cpp/exceptions.h \
  /usr/include/yaml-cpp/mark.h \
  /usr/include/yaml-cpp/noexcept.h \
  /usr/include/yaml-cpp/traits.h \
  /usr/include/yaml-cpp/node/node.h \
  /usr/include/yaml-cpp/node/detail/iterator_fwd.h \
  /usr/include/yaml-cpp/node/ptr.h \
  /usr/include/yaml-cpp/node/type.h \
  /usr/include/yaml-cpp/node/impl.h \
  /usr/include/yaml-cpp/node/detail/memory.h \
  /usr/include/yaml-cpp/node/detail/node.h \
  /usr/include/yaml-cpp/node/detail/node_ref.h \
  /usr/include/yaml-cpp/node/detail/node_data.h \
  /usr/include/yaml-cpp/node/detail/node_iterator.h \
  /usr/include/c++/11/iterator \
  /usr/include/c++/11/bits/stream_iterator.h \
  /usr/include/yaml-cpp/node/iterator.h \
  /usr/include/yaml-cpp/node/detail/iterator.h \
  /usr/include/c++/11/atomic \
  /usr/include/yaml-cpp/node/convert.h \
  /usr/include/yaml-cpp/node/detail/impl.h \
  /usr/include/c++/11/algorithm \
  /usr/include/c++/11/bits/stl_algo.h \
  /usr/include/c++/11/bits/algorithmfwd.h \
  /usr/include/c++/11/bits/stl_heap.h \
  /usr/include/yaml-cpp/node/parse.h \
  /usr/include/yaml-cpp/node/emit.h \
  ../include/pixel_converter.h \
  /usr/include/c++/11/iomanip \
  /usr/include/c++/11/locale \
  /usr/include/c++/11/bits/locale_facets_nonio.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/time_members.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/messages_members.h \
  /usr/include/libintl.h \
  /usr/include/c++/11/bits/locale_facets_nonio.tcc \
  /usr/include/c++/11/bits/locale_conv.h \
  /usr/include/c++/11/bits/quoted_string.h \
  /usr/include/c++/11/cstring \
  /usr/include/string.h \
  /usr/include/strings.h

CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o: ../src/pixel_converter.cpp \
  /usr/include/stdc-predef.h \
  ../include/pixel_converter.h \
  /usr/include/c++/11/string \
  /usr/include/x86_64-linux-gnu/c++/11/bits/c++config.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/os_defines.h \
  /usr/include/features.h \
  /usr/include/features-time64.h \
  /usr/include/x86_64-linux-gnu/bits/wordsize.h \
  /usr/include/x86_64-linux-gnu/bits/timesize.h \
  /usr/include/x86_64-linux-gnu/sys/cdefs.h \
  /usr/include/x86_64-linux-gnu/bits/long-double.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs.h \
  /usr/include/x86_64-linux-gnu/gnu/stubs-64.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/cpu_defines.h \
  /usr/include/c++/11/bits/stringfwd.h \
  /usr/include/c++/11/bits/memoryfwd.h \
  /usr/include/c++/11/bits/char_traits.h \
  /usr/include/c++/11/bits/stl_algobase.h \
  /usr/include/c++/11/bits/functexcept.h \
  /usr/include/c++/11/bits/exception_defines.h \
  /usr/include/c++/11/bits/cpp_type_traits.h \
  /usr/include/c++/11/ext/type_traits.h \
  /usr/include/c++/11/ext/numeric_traits.h \
  /usr/include/c++/11/bits/stl_pair.h \
  /usr/include/c++/11/bits/move.h \
  /usr/include/c++/11/type_traits \
  /usr/include/c++/11/bits/stl_iterator_base_types.h \
  /usr/include/c++/11/bits/stl_iterator_base_funcs.h \
  /usr/include/c++/11/bits/concept_check.h \
  /usr/include/c++/11/debug/assertions.h \
  /usr/include/c++/11/bits/stl_iterator.h \
  /usr/include/c++/11/bits/ptr_traits.h \
  /usr/include/c++/11/debug/debug.h \
  /usr/include/c++/11/bits/predefined_ops.h \
  /usr/include/c++/11/bits/postypes.h \
  /usr/include/c++/11/cwchar \
  /usr/include/wchar.h \
  /usr/include/x86_64-linux-gnu/bits/libc-header-start.h \
  /usr/include/x86_64-linux-gnu/bits/floatn.h \
  /usr/include/x86_64-linux-gnu/bits/floatn-common.h \
  /usr/lib/gcc/x86_64-linux-gnu/11/include/stddef.h \
  /usr/lib/gcc/x86_64-linux-gnu/11/include/stdarg.h \
  /usr/include/x86_64-linux-gnu/bits/wchar.h \
  /usr/include/x86_64-linux-gnu/bits/types/wint_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/locale_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h \
  /usr/include/c++/11/cstdint \
  /usr/lib/gcc/x86_64-linux-gnu/11/include/stdint.h \
  /usr/include/stdint.h \
  /usr/include/x86_64-linux-gnu/bits/types.h \
  /usr/include/x86_64-linux-gnu/bits/typesizes.h \
  /usr/include/x86_64-linux-gnu/bits/time64.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-intn.h \
  /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h \
  /usr/include/c++/11/bits/allocator.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/c++allocator.h \
  /usr/include/c++/11/ext/new_allocator.h \
  /usr/include/c++/11/new \
  /usr/include/c++/11/bits/exception.h \
  /usr/include/c++/11/bits/localefwd.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/c++locale.h \
  /usr/include/c++/11/clocale \
  /usr/include/locale.h \
  /usr/include/x86_64-linux-gnu/bits/locale.h \
  /usr/include/c++/11/iosfwd \
  /usr/include/c++/11/cctype \
  /usr/include/ctype.h \
  /usr/include/x86_64-linux-gnu/bits/endian.h \
  /usr/include/x86_64-linux-gnu/bits/endianness.h \
  /usr/include/c++/11/bits/ostream_insert.h \
  /usr/include/c++/11/bits/cxxabi_forced.h \
  /usr/include/c++/11/bits/stl_function.h \
  /usr/include/c++/11/backward/binders.h \
  /usr/include/c++/11/bits/range_access.h \
  /usr/include/c++/11/initializer_list \
  /usr/include/c++/11/bits/basic_string.h \
  /usr/include/c++/11/ext/atomicity.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/gthr.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/gthr-default.h \
  /usr/include/pthread.h \
  /usr/include/sched.h \
  /usr/include/x86_64-linux-gnu/bits/types/time_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h \
  /usr/include/x86_64-linux-gnu/bits/sched.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h \
  /usr/include/x86_64-linux-gnu/bits/cpu-set.h \
  /usr/include/time.h \
  /usr/include/x86_64-linux-gnu/bits/time.h \
  /usr/include/x86_64-linux-gnu/bits/timex.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h \
  /usr/include/x86_64-linux-gnu/bits/types/clock_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h \
  /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/timer_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h \
  /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h \
  /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h \
  /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h \
  /usr/include/x86_64-linux-gnu/bits/struct_mutex.h \
  /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h \
  /usr/include/x86_64-linux-gnu/bits/setjmp.h \
  /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h \
  /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/atomic_word.h \
  /usr/include/x86_64-linux-gnu/sys/single_threaded.h \
  /usr/include/c++/11/ext/alloc_traits.h \
  /usr/include/c++/11/bits/alloc_traits.h \
  /usr/include/c++/11/bits/stl_construct.h \
  /usr/include/c++/11/ext/string_conversions.h \
  /usr/include/c++/11/cstdlib \
  /usr/include/stdlib.h \
  /usr/include/x86_64-linux-gnu/bits/waitflags.h \
  /usr/include/x86_64-linux-gnu/bits/waitstatus.h \
  /usr/include/x86_64-linux-gnu/sys/types.h \
  /usr/include/endian.h \
  /usr/include/x86_64-linux-gnu/bits/byteswap.h \
  /usr/include/x86_64-linux-gnu/bits/uintn-identity.h \
  /usr/include/x86_64-linux-gnu/sys/select.h \
  /usr/include/x86_64-linux-gnu/bits/select.h \
  /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h \
  /usr/include/alloca.h \
  /usr/include/x86_64-linux-gnu/bits/stdlib-float.h \
  /usr/include/c++/11/bits/std_abs.h \
  /usr/include/c++/11/cstdio \
  /usr/include/stdio.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h \
  /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h \
  /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h \
  /usr/include/x86_64-linux-gnu/bits/stdio_lim.h \
  /usr/include/c++/11/cerrno \
  /usr/include/errno.h \
  /usr/include/x86_64-linux-gnu/bits/errno.h \
  /usr/include/linux/errno.h \
  /usr/include/x86_64-linux-gnu/asm/errno.h \
  /usr/include/asm-generic/errno.h \
  /usr/include/asm-generic/errno-base.h \
  /usr/include/x86_64-linux-gnu/bits/types/error_t.h \
  /usr/include/c++/11/bits/charconv.h \
  /usr/include/c++/11/bits/functional_hash.h \
  /usr/include/c++/11/bits/hash_bytes.h \
  /usr/include/c++/11/bits/basic_string.tcc \
  /usr/include/c++/11/iostream \
  /usr/include/c++/11/ostream \
  /usr/include/c++/11/ios \
  /usr/include/c++/11/exception \
  /usr/include/c++/11/bits/exception_ptr.h \
  /usr/include/c++/11/bits/cxxabi_init_exception.h \
  /usr/include/c++/11/typeinfo \
  /usr/include/c++/11/bits/nested_exception.h \
  /usr/include/c++/11/bits/ios_base.h \
  /usr/include/c++/11/bits/locale_classes.h \
  /usr/include/c++/11/bits/locale_classes.tcc \
  /usr/include/c++/11/system_error \
  /usr/include/x86_64-linux-gnu/c++/11/bits/error_constants.h \
  /usr/include/c++/11/stdexcept \
  /usr/include/c++/11/streambuf \
  /usr/include/c++/11/bits/streambuf.tcc \
  /usr/include/c++/11/bits/basic_ios.h \
  /usr/include/c++/11/bits/locale_facets.h \
  /usr/include/c++/11/cwctype \
  /usr/include/wctype.h \
  /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/ctype_base.h \
  /usr/include/c++/11/bits/streambuf_iterator.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/ctype_inline.h \
  /usr/include/c++/11/bits/locale_facets.tcc \
  /usr/include/c++/11/bits/basic_ios.tcc \
  /usr/include/c++/11/bits/ostream.tcc \
  /usr/include/c++/11/istream \
  /usr/include/c++/11/bits/istream.tcc \
  /usr/include/c++/11/fstream \
  /usr/include/c++/11/bits/codecvt.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/basic_file.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/c++io.h \
  /usr/include/c++/11/bits/fstream.tcc \
  /usr/include/c++/11/vector \
  /usr/include/c++/11/bits/stl_uninitialized.h \
  /usr/include/c++/11/bits/stl_vector.h \
  /usr/include/c++/11/bits/stl_bvector.h \
  /usr/include/c++/11/bits/vector.tcc \
  /usr/include/c++/11/utility \
  /usr/include/c++/11/bits/stl_relops.h \
  /usr/include/c++/11/random \
  /usr/include/c++/11/cmath \
  /usr/include/math.h \
  /usr/include/x86_64-linux-gnu/bits/math-vector.h \
  /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h \
  /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h \
  /usr/include/x86_64-linux-gnu/bits/fp-logb.h \
  /usr/include/x86_64-linux-gnu/bits/fp-fast.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls.h \
  /usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h \
  /usr/include/x86_64-linux-gnu/bits/iscanonical.h \
  /usr/include/c++/11/limits \
  /usr/include/c++/11/bits/random.h \
  /usr/include/c++/11/bits/uniform_int_dist.h \
  /usr/include/x86_64-linux-gnu/c++/11/bits/opt_random.h \
  /usr/include/c++/11/bits/random.tcc \
  /usr/include/c++/11/numeric \
  /usr/include/c++/11/bits/stl_numeric.h \
  /usr/include/c++/11/bit \
  /usr/include/c++/11/chrono \
  /usr/include/c++/11/ratio \
  /usr/include/c++/11/ctime \
  /usr/include/c++/11/bits/parse_numbers.h


../src/pixel_converter.cpp:

/usr/include/strings.h:

/usr/include/c++/11/bits/quoted_string.h:

/usr/include/c++/11/bits/locale_conv.h:

/usr/include/c++/11/bits/locale_facets_nonio.tcc:

/usr/include/libintl.h:

/usr/include/x86_64-linux-gnu/c++/11/bits/messages_members.h:

/usr/include/x86_64-linux-gnu/c++/11/bits/time_members.h:

/usr/include/c++/11/bits/locale_facets_nonio.h:

/usr/include/yaml-cpp/node/emit.h:

/usr/include/yaml-cpp/node/parse.h:

/usr/include/c++/11/bits/algorithmfwd.h:

/usr/include/c++/11/bits/stl_algo.h:

/usr/include/c++/11/algorithm:

/usr/include/yaml-cpp/node/detail/impl.h:

/usr/include/yaml-cpp/node/convert.h:

/usr/include/c++/11/atomic:

/usr/include/yaml-cpp/node/iterator.h:

/usr/include/c++/11/bits/stream_iterator.h:

/usr/include/yaml-cpp/node/detail/node_data.h:

/usr/include/yaml-cpp/node/detail/memory.h:

/usr/include/yaml-cpp/node/impl.h:

/usr/include/yaml-cpp/node/detail/node.h:

/usr/include/yaml-cpp/node/detail/iterator_fwd.h:

/usr/include/yaml-cpp/node/node.h:

/usr/include/yaml-cpp/traits.h:

/usr/include/yaml-cpp/mark.h:

/usr/include/yaml-cpp/exceptions.h:

/usr/include/c++/11/bits/stl_multimap.h:

/usr/include/c++/11/bits/stl_map.h:

/usr/include/x86_64-linux-gnu/sys/select.h:

/usr/include/stdlib.h:

/usr/include/c++/11/bits/cxxabi_forced.h:

/usr/include/c++/11/fstream:

/usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h:

/usr/include/x86_64-linux-gnu/bits/struct_rwlock.h:

/usr/include/c++/11/numeric:

/usr/include/c++/11/cstdlib:

/usr/include/c++/11/bits/stl_iterator_base_types.h:

/usr/include/c++/11/bit:

/usr/include/x86_64-linux-gnu/bits/thread-shared-types.h:

/usr/include/c++/11/ext/new_allocator.h:

/usr/include/x86_64-linux-gnu/bits/pthreadtypes.h:

/usr/include/c++/11/bits/allocated_ptr.h:

/usr/include/x86_64-linux-gnu/sys/types.h:

/usr/include/c++/11/ext/aligned_buffer.h:

/usr/include/x86_64-linux-gnu/bits/types/timer_t.h:

/usr/include/c++/11/ext/atomicity.h:

/usr/include/c++/11/bits/stl_pair.h:

/usr/include/x86_64-linux-gnu/bits/setjmp.h:

/usr/include/time.h:

/usr/include/x86_64-linux-gnu/bits/cpu-set.h:

/usr/include/pthread.h:

/usr/include/x86_64-linux-gnu/c++/11/bits/gthr-default.h:

/usr/include/c++/11/ext/concurrence.h:

/usr/include/c++/11/bits/shared_ptr_base.h:

/usr/include/c++/11/bits/basic_string.h:

/usr/include/x86_64-linux-gnu/bits/endian.h:

/usr/include/x86_64-linux-gnu/gnu/stubs.h:

/usr/include/c++/11/backward/binders.h:

/usr/include/x86_64-linux-gnu/bits/time.h:

/usr/include/x86_64-linux-gnu/bits/select.h:

/usr/include/x86_64-linux-gnu/bits/endianness.h:

/usr/include/x86_64-linux-gnu/bits/locale.h:

/usr/include/locale.h:

/usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h:

/usr/include/x86_64-linux-gnu/bits/waitflags.h:

/usr/include/c++/11/bits/localefwd.h:

/usr/include/x86_64-linux-gnu/c++/11/bits/c++allocator.h:

/usr/include/yaml-cpp/binary.h:

/usr/include/x86_64-linux-gnu/bits/uintn-identity.h:

/usr/include/c++/11/bits/random.tcc:

/usr/include/x86_64-linux-gnu/bits/stdlib-float.h:

/usr/include/c++/11/locale:

/usr/include/ctype.h:

/usr/include/c++/11/ext/alloc_traits.h:

/usr/include/x86_64-linux-gnu/bits/sched.h:

/usr/include/c++/11/bits/alloc_traits.h:

/usr/include/stdc-predef.h:

/usr/include/x86_64-linux-gnu/c++/11/bits/atomic_word.h:

/usr/include/c++/11/tuple:

/usr/include/c++/11/bits/stl_tree.h:

/usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h:

/usr/include/c++/11/cstdint:

/usr/include/x86_64-linux-gnu/bits/waitstatus.h:

/usr/include/x86_64-linux-gnu/c++/11/bits/gthr.h:

/usr/include/c++/11/bits/functexcept.h:

/usr/include/c++/11/streambuf:

/usr/include/x86_64-linux-gnu/c++/11/bits/c++locale.h:

/usr/include/c++/11/bits/stl_algobase.h:

/usr/include/c++/11/cwchar:

/usr/lib/gcc/x86_64-linux-gnu/11/include/stdint.h:

/usr/include/c++/11/initializer_list:

/usr/include/c++/11/iosfwd:

/usr/include/x86_64-linux-gnu/bits/time64.h:

/usr/include/c++/11/ctime:

/usr/include/c++/11/ext/type_traits.h:

/usr/include/c++/11/bits/exception_defines.h:

/usr/include/x86_64-linux-gnu/c++/11/bits/os_defines.h:

/usr/include/c++/11/type_traits:

/usr/include/x86_64-linux-gnu/sys/single_threaded.h:

/usr/include/c++/11/cmath:

/usr/include/c++/11/bits/allocator.h:

/usr/include/c++/11/bits/char_traits.h:

/usr/include/asm-generic/errno-base.h:

/usr/include/c++/11/ext/numeric_traits.h:

/usr/include/yaml-cpp/node/type.h:

/usr/include/c++/11/bits/atomic_base.h:

/usr/include/features.h:

/usr/include/yaml-cpp/node/detail/iterator.h:

/usr/include/c++/11/bits/stringfwd.h:

/usr/include/c++/11/bits/stl_function.h:

/usr/include/c++/11/bits/memoryfwd.h:

/usr/include/x86_64-linux-gnu/bits/types/FILE.h:

/usr/include/x86_64-linux-gnu/bits/types/time_t.h:

/usr/include/c++/11/string:

../include/detection_processor.h:

/usr/include/x86_64-linux-gnu/bits/stdint-uintn.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h:

/usr/include/x86_64-linux-gnu/bits/types/__locale_t.h:

/usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h:

/usr/include/c++/11/bits/ostream_insert.h:

/usr/include/c++/11/map:

/usr/include/x86_64-linux-gnu/bits/wchar.h:

/usr/include/x86_64-linux-gnu/bits/types.h:

/usr/include/x86_64-linux-gnu/bits/stdint-intn.h:

/usr/include/x86_64-linux-gnu/bits/types/locale_t.h:

/usr/include/x86_64-linux-gnu/bits/wctype-wchar.h:

/usr/include/x86_64-linux-gnu/c++/11/bits/c++config.h:

/usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h:

/usr/include/linux/errno.h:

/usr/include/x86_64-linux-gnu/gnu/stubs-64.h:

/usr/include/yaml-cpp/ostream_wrapper.h:

/usr/include/c++/11/bits/cpp_type_traits.h:

/usr/include/c++/11/bits/stl_iterator_base_funcs.h:

/usr/include/yaml-cpp/stlemitter.h:

/usr/include/c++/11/debug/debug.h:

/usr/include/features-time64.h:

/usr/include/yaml-cpp/node/detail/node_ref.h:

/usr/include/c++/11/ext/string_conversions.h:

/usr/include/c++/11/list:

/usr/include/x86_64-linux-gnu/bits/stdio_lim.h:

/usr/include/x86_64-linux-gnu/bits/timex.h:

/usr/include/c++/11/bits/invoke.h:

/usr/include/yaml-cpp/node/detail/node_iterator.h:

/usr/include/x86_64-linux-gnu/bits/timesize.h:

/usr/include/x86_64-linux-gnu/c++/11/bits/cpu_defines.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h:

/usr/include/c++/11/iterator:

/usr/include/c++/11/bits/range_access.h:

/usr/include/x86_64-linux-gnu/bits/floatn.h:

/usr/include/c++/11/debug/assertions.h:

/usr/lib/gcc/x86_64-linux-gnu/11/include/stdarg.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h:

/usr/include/x86_64-linux-gnu/asm/errno.h:

/usr/include/x86_64-linux-gnu/sys/cdefs.h:

/usr/include/x86_64-linux-gnu/bits/byteswap.h:

/usr/include/c++/11/bits/stl_construct.h:

/usr/include/c++/11/bits/shared_ptr.h:

/usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h:

/usr/include/x86_64-linux-gnu/bits/long-double.h:

/usr/include/x86_64-linux-gnu/bits/types/sigset_t.h:

/usr/include/c++/11/bits/concept_check.h:

/usr/include/yaml-cpp/node/ptr.h:

/usr/include/x86_64-linux-gnu/bits/struct_mutex.h:

/usr/include/c++/11/bits/stl_iterator.h:

/usr/include/c++/11/bits/sstream.tcc:

/usr/include/c++/11/new:

/usr/include/c++/11/clocale:

/usr/include/c++/11/bits/ostream.tcc:

/usr/include/wchar.h:

/usr/include/x86_64-linux-gnu/bits/libc-header-start.h:

/usr/include/x86_64-linux-gnu/bits/floatn-common.h:

/usr/include/x86_64-linux-gnu/bits/types/wint_t.h:

/usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h:

/usr/include/x86_64-linux-gnu/bits/types/clock_t.h:

/usr/include/c++/11/bits/stl_tempbuf.h:

/usr/include/c++/11/bits/predefined_ops.h:

/usr/include/x86_64-linux-gnu/bits/typesizes.h:

/usr/include/c++/11/iomanip:

/usr/include/c++/11/cctype:

/usr/include/alloca.h:

/usr/include/c++/11/bits/stl_heap.h:

/usr/include/c++/11/bits/std_abs.h:

/usr/include/x86_64-linux-gnu/bits/wordsize.h:

/usr/include/c++/11/chrono:

/usr/include/stdio.h:

/usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h:

/usr/include/c++/11/cerrno:

/usr/include/errno.h:

/usr/include/x86_64-linux-gnu/bits/errno.h:

/usr/include/sched.h:

/usr/include/wctype.h:

/usr/include/x86_64-linux-gnu/c++/11/bits/ctype_base.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h:

/usr/include/asm-generic/errno.h:

/usr/include/x86_64-linux-gnu/bits/types/error_t.h:

/usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h:

/usr/include/c++/11/bits/charconv.h:

/usr/include/c++/11/bits/functional_hash.h:

/usr/include/c++/11/bits/move.h:

/usr/include/c++/11/bits/hash_bytes.h:

/usr/include/c++/11/bits/basic_string.tcc:

/usr/include/c++/11/bits/stl_relops.h:

../include/pixel_converter.h:

/usr/include/c++/11/memory:

/usr/include/c++/11/ostream:

/usr/include/c++/11/bits/unique_ptr.h:

/usr/include/c++/11/exception:

/usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h:

/usr/include/c++/11/typeinfo:

/usr/include/c++/11/bits/nested_exception.h:

/usr/include/c++/11/bits/ios_base.h:

/usr/include/x86_64-linux-gnu/bits/fp-logb.h:

/usr/include/c++/11/set:

/usr/include/c++/11/bits/locale_classes.h:

/usr/include/c++/11/bits/locale_classes.tcc:

/usr/include/c++/11/iostream:

/usr/include/x86_64-linux-gnu/bits/fp-fast.h:

/usr/include/c++/11/system_error:

/usr/include/c++/11/cstddef:

/usr/include/c++/11/bits/stl_vector.h:

/usr/include/c++/11/ios:

/usr/include/c++/11/utility:

/usr/include/x86_64-linux-gnu/c++/11/bits/error_constants.h:

/usr/include/c++/11/stdexcept:

/usr/include/string.h:

/usr/include/c++/11/bits/streambuf.tcc:

/usr/include/yaml-cpp/noexcept.h:

/usr/include/c++/11/bits/atomic_lockfree_defines.h:

/usr/include/c++/11/bits/uniform_int_dist.h:

/usr/include/c++/11/bits/basic_ios.h:

/usr/include/c++/11/bits/exception.h:

/usr/include/c++/11/bits/locale_facets.h:

/usr/include/c++/11/cwctype:

/usr/include/c++/11/bits/streambuf_iterator.h:

/usr/include/c++/11/cstdio:

/usr/include/yaml-cpp/emitter.h:

/usr/include/x86_64-linux-gnu/c++/11/bits/ctype_inline.h:

/usr/include/c++/11/random:

/usr/include/c++/11/bits/list.tcc:

/usr/include/x86_64-linux-gnu/bits/types/struct_tm.h:

/usr/include/c++/11/bits/locale_facets.tcc:

/usr/include/c++/11/istream:

/usr/include/c++/11/bits/istream.tcc:

/usr/include/x86_64-linux-gnu/c++/11/bits/basic_file.h:

/usr/include/x86_64-linux-gnu/c++/11/bits/c++io.h:

/usr/include/c++/11/bits/fstream.tcc:

/usr/include/c++/11/vector:

/usr/include/c++/11/bits/align.h:

/usr/include/c++/11/bits/stl_uninitialized.h:

/usr/include/c++/11/bits/stl_numeric.h:

/usr/include/c++/11/backward/auto_ptr.h:

/usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h:

/usr/include/x86_64-linux-gnu/bits/types/clockid_t.h:

/usr/include/c++/11/bits/stl_bvector.h:

/usr/include/c++/11/bits/vector.tcc:

/usr/include/c++/11/cstring:

/usr/include/x86_64-linux-gnu/bits/types/__FILE.h:

/usr/include/math.h:

/usr/include/x86_64-linux-gnu/bits/math-vector.h:

/usr/lib/gcc/x86_64-linux-gnu/11/include/stddef.h:

/usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h:

/usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h:

/usr/include/x86_64-linux-gnu/bits/flt-eval-method.h:

/usr/include/c++/11/bits/codecvt.h:

/usr/include/c++/11/bits/basic_ios.tcc:

/usr/include/x86_64-linux-gnu/c++/11/bits/opt_random.h:

/usr/include/x86_64-linux-gnu/bits/mathcalls.h:

/usr/include/c++/11/bits/random.h:

/usr/include/c++/11/bits/postypes.h:

../src/detection_processor.cpp:

/usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h:

/usr/include/c++/11/limits:

/usr/include/c++/11/ratio:

/usr/include/c++/11/bits/parse_numbers.h:

/usr/include/c++/11/bits/shared_ptr_atomic.h:

/usr/include/yaml-cpp/yaml.h:

/usr/include/c++/11/bits/cxxabi_init_exception.h:

/usr/include/yaml-cpp/parser.h:

/usr/include/c++/11/bits/stl_raw_storage_iter.h:

/usr/include/c++/11/bits/uses_allocator.h:

/usr/include/c++/11/array:

/usr/include/c++/11/bits/refwrap.h:

/usr/include/c++/11/bits/exception_ptr.h:

/usr/include/yaml-cpp/dll.h:

/usr/include/yaml-cpp/emitterstyle.h:

/usr/include/c++/11/sstream:

/usr/include/yaml-cpp/emitterdef.h:

/usr/include/yaml-cpp/emittermanip.h:

/usr/include/yaml-cpp/null.h:

/usr/include/x86_64-linux-gnu/bits/iscanonical.h:

/usr/include/c++/11/bits/stl_list.h:

/usr/include/c++/11/bits/stl_set.h:

/usr/include/c++/11/bits/stl_multiset.h:

/usr/include/endian.h:

/usr/include/stdint.h:

/usr/include/c++/11/bits/ptr_traits.h:

/usr/include/c++/11/bits/erase_if.h:
