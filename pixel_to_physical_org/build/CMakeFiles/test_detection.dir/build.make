# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/panpan/code/Calib/pixel_to_physical

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/panpan/code/Calib/pixel_to_physical/build

# Include any dependencies generated for this target.
include CMakeFiles/test_detection.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_detection.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_detection.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_detection.dir/flags.make

CMakeFiles/test_detection.dir/src/main.cpp.o: CMakeFiles/test_detection.dir/flags.make
CMakeFiles/test_detection.dir/src/main.cpp.o: ../src/main.cpp
CMakeFiles/test_detection.dir/src/main.cpp.o: CMakeFiles/test_detection.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/panpan/code/Calib/pixel_to_physical/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_detection.dir/src/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_detection.dir/src/main.cpp.o -MF CMakeFiles/test_detection.dir/src/main.cpp.o.d -o CMakeFiles/test_detection.dir/src/main.cpp.o -c /home/<USER>/panpan/code/Calib/pixel_to_physical/src/main.cpp

CMakeFiles/test_detection.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/test_detection.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/panpan/code/Calib/pixel_to_physical/src/main.cpp > CMakeFiles/test_detection.dir/src/main.cpp.i

CMakeFiles/test_detection.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/test_detection.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/panpan/code/Calib/pixel_to_physical/src/main.cpp -o CMakeFiles/test_detection.dir/src/main.cpp.s

# Object files for target test_detection
test_detection_OBJECTS = \
"CMakeFiles/test_detection.dir/src/main.cpp.o"

# External object files for target test_detection
test_detection_EXTERNAL_OBJECTS =

bin/test_detection: CMakeFiles/test_detection.dir/src/main.cpp.o
bin/test_detection: CMakeFiles/test_detection.dir/build.make
bin/test_detection: lib/libdetection_processor.so.1.0.0
bin/test_detection: CMakeFiles/test_detection.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/panpan/code/Calib/pixel_to_physical/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bin/test_detection"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_detection.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_detection.dir/build: bin/test_detection
.PHONY : CMakeFiles/test_detection.dir/build

CMakeFiles/test_detection.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_detection.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_detection.dir/clean

CMakeFiles/test_detection.dir/depend:
	cd /home/<USER>/panpan/code/Calib/pixel_to_physical/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/panpan/code/Calib/pixel_to_physical /home/<USER>/panpan/code/Calib/pixel_to_physical /home/<USER>/panpan/code/Calib/pixel_to_physical/build /home/<USER>/panpan/code/Calib/pixel_to_physical/build /home/<USER>/panpan/code/Calib/pixel_to_physical/build/CMakeFiles/test_detection.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/test_detection.dir/depend

