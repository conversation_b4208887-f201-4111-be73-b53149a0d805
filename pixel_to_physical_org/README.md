# Pixel to Physical Distance Converter

这个项目是一个用于将图像中的像素坐标转换为实际物理距离的工具。它主要用于计算机视觉和机器人应用中，帮助计算检测到的物体到相机的实际距离。

## 功能特点

- 支持将像素坐标转换为物理距离
- 可配置的物体尺寸范围
- 基于 YAML 的配置文件系统
- 支持多种物体类型的检测结果处理

## 项目结构

```
pixel_to_physical/
├── include/                 # 头文件目录
│   ├── detection_processor.h
│   └── pixel_converter.h
├── src/                    # 源代码目录
│   └── main.cpp
├── config/                 # 配置文件目录
│   └── size_ranges.yaml
├── test/                   # 测试文件目录
├── CMakeLists.txt         # CMake 构建文件
└── distance_table         # 距离查找表
```

## 依赖项

- C++11 或更高版本
- CMake 3.10 或更高版本
- yaml-cpp 库
- OpenCV（可选，用于图像处理）

## 安装说明

1. 克隆仓库：
```bash
git clone [repository_url]
cd pixel_to_physical
```

2. 创建构建目录：
```bash
mkdir build && cd build
```

3. 配置和构建项目：
```bash
cmake ..
make
```

## 使用方法

1. 配置 `config/size_ranges.yaml` 文件，设置不同物体类型的尺寸范围。

2. 在代码中使用：
```cpp
#include <detection_processor.h>

// 创建检测处理器实例
DetectionProcessor processor;

// 加载配置文件
YAML::Node config = YAML::LoadFile("size_ranges.yaml");
processor.setSizeRangesConfig(config);

// 处理检测结果
DetectionResult det = {
    {x1, y1, x2, y2},  // 边界框坐标
    "object_type",     // 物体类型
    confidence         // 置信度
};

processor.processDetectionResult(&det, "distance_table");
```

## 配置说明

在 `config/size_ranges.yaml` 文件中，您可以配置不同物体类型的尺寸范围。配置文件格式如下：

```yaml
object_types:
  trash_bin:
    max_size: 20
    min_size: 50
```

## 注意事项

- 确保 `distance_table` 文件存在且格式正确
- 检测结果的边界框坐标需要按照 [x1, y1, x2, y2] 的格式提供
- 物体类型需要在配置文件中定义

## 许可证

[添加许可证信息]

## 贡献

欢迎提交 Issue 和 Pull Request 来帮助改进这个项目。 