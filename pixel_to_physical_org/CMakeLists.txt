cmake_minimum_required(VERSION 3.10)
project(detection_processor)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 添加头文件目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)

# 添加源文件
set(SOURCES
    src/detection_processor.cpp
    src/pixel_converter.cpp
)

# Find YAML-CPP package
find_package(yaml-cpp REQUIRED)

# 创建动态库
add_library(detection_processor SHARED ${SOURCES})

# 设置库的属性
set_target_properties(detection_processor PROPERTIES
    VERSION 1.0.0
    SOVERSION 1
    PUBLIC_HEADER include/detection_processor.h
    PUBLIC_HEADER include/pixel_converter.h
    OUTPUT_NAME "detection_processor"
    PREFIX "lib"
)

# 链接依赖库
target_link_libraries(detection_processor PRIVATE yaml-cpp)

# 添加测试程序
add_executable(test_detection src/main.cpp)
target_link_libraries(test_detection PRIVATE detection_processor yaml-cpp)

# 安装规则
install(TARGETS detection_processor test_detection
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
    PUBLIC_HEADER DESTINATION include
)

# 导出头文件
install(FILES 
    include/detection_processor.h
    include/pixel_converter.h
    DESTINATION include/detection_processor
) 