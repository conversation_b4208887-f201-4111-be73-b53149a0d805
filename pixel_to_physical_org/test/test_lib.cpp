#include <detection_processor/detection_processor.h>
#include <yaml-cpp/yaml.h>
#include <iostream>

int main() {
    try {
        // 加载配置文件
        YAML::Node config = YAML::LoadFile("../config/size_ranges.yaml");
        
        // 创建检测处理器实例
        DetectionProcessor processor;
        processor.setSizeRangesConfig(config);

        // 创建检测结果
        DetectionResult det = {
            {230, 550,  // 左上角
             900, 600}, // 右下角
            "trash",    // 标签
            0.95,       // 置信度
        };

        // 处理检测结果
        processor.processDetectionResult(&det, "../distance_table");
        
        std::cout << "最终物理距离: " << det.physical_distance << " cm" << std::endl;
        
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
} 