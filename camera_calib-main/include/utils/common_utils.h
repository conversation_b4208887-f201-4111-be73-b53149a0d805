/**
 * @file common_utils.h
 * @brief 通用工具函数和宏定义
 * <AUTHOR> Calibration Team
 * @date 2024
 */

#ifndef CAMERA_CALIBRATION_UTILS_COMMON_UTILS_H
#define CAMERA_CALIBRATION_UTILS_COMMON_UTILS_H

#include <opencv2/opencv.hpp>
#include <string>
#include <functional>
#include <stdexcept>

namespace camera_calibration {
namespace utils {

/**
 * @brief 通用日志记录宏 - 使用统一的日志系统
 */
#define LOG_ERROR_FUNC(class_name, message) \
    do { \
        std::cerr << "[" << class_name << " ERROR] " << message << std::endl; \
    } while(0)

#define LOG_INFO_FUNC(class_name, message) \
    do { \
        std::cout << "[" << class_name << " INFO] " << message << std::endl; \
    } while(0)

#define LOG_DEBUG_FUNC(class_name, message, debug_mode) \
    do { \
        if (debug_mode) { \
            std::cout << "[" << class_name << " DEBUG] " << message << std::endl; \
        } \
    } while(0)

/**
 * @brief 通用异常处理宏
 */
#define HANDLE_EXCEPTION(operation, error_msg, return_value) \
    try { \
        operation; \
    } catch (const cv::Exception& e) { \
        LOG_ERROR_FUNC("OpenCV", error_msg + ": " + std::string(e.what())); \
        return return_value; \
    } catch (const std::exception& e) { \
        LOG_ERROR_FUNC("Exception", error_msg + ": " + std::string(e.what())); \
        return return_value; \
    }

/**
 * @brief 图像验证工具类
 */
class ImageValidator {
public:
    /**
     * @brief 验证图像是否有效
     * @param image 待验证的图像
     * @param class_name 调用类名（用于日志）
     * @return 验证是否通过
     */
    static bool validateImage(const cv::Mat& image, const std::string& class_name = "ImageValidator");
    
    /**
     * @brief 验证图像尺寸
     * @param image 待验证的图像
     * @param min_width 最小宽度
     * @param min_height 最小高度
     * @param class_name 调用类名（用于日志）
     * @return 验证是否通过
     */
    static bool validateImageSize(const cv::Mat& image, int min_width, int min_height, 
                                 const std::string& class_name = "ImageValidator");
    
    /**
     * @brief 验证图像通道数
     * @param image 待验证的图像
     * @param expected_channels 期望的通道数
     * @param class_name 调用类名（用于日志）
     * @return 验证是否通过
     */
    static bool validateImageChannels(const cv::Mat& image, int expected_channels,
                                     const std::string& class_name = "ImageValidator");
};

/**
 * @brief 文件操作工具类
 */
class FileOperationHelper {
public:
    /**
     * @brief 安全保存图像文件
     * @param image 待保存的图像
     * @param filename 文件名
     * @param output_path 输出路径
     * @param class_name 调用类名（用于日志）
     * @return 保存是否成功
     */
    static bool saveImageSafely(const cv::Mat& image, const std::string& filename,
                               const std::string& output_path, 
                               const std::string& class_name = "FileOperationHelper");
    
    /**
     * @brief 创建输出目录
     * @param output_path 输出路径
     * @param class_name 调用类名（用于日志）
     * @return 创建是否成功
     */
    static bool createOutputDirectory(const std::string& output_path,
                                     const std::string& class_name = "FileOperationHelper");
};

/**
 * @brief 异常处理工具类
 */
class ExceptionHandler {
public:
    /**
     * @brief 执行带异常处理的操作
     * @param operation 要执行的操作
     * @param error_message 错误消息
     * @param class_name 调用类名
     * @return 操作是否成功
     */
    template<typename Func>
    static bool executeWithExceptionHandling(Func&& operation, 
                                            const std::string& error_message,
                                            const std::string& class_name) {
        try {
            operation();
            return true;
        } catch (const cv::Exception& e) {
            LOG_ERROR_FUNC(class_name, error_message + " (OpenCV异常): " + std::string(e.what()));
            return false;
        } catch (const std::exception& e) {
            LOG_ERROR_FUNC(class_name, error_message + " (标准异常): " + std::string(e.what()));
            return false;
        } catch (...) {
            LOG_ERROR_FUNC(class_name, error_message + " (未知异常)");
            return false;
        }
    }
    
    /**
     * @brief 执行带异常处理和返回值的操作
     * @param operation 要执行的操作
     * @param default_value 默认返回值
     * @param error_message 错误消息
     * @param class_name 调用类名
     * @return 操作结果或默认值
     */
    template<typename Func, typename ReturnType>
    static ReturnType executeWithExceptionHandling(Func&& operation, 
                                                   ReturnType default_value,
                                                   const std::string& error_message,
                                                   const std::string& class_name) {
        try {
            return operation();
        } catch (const cv::Exception& e) {
            LOG_ERROR_FUNC(class_name, error_message + " (OpenCV异常): " + std::string(e.what()));
            return default_value;
        } catch (const std::exception& e) {
            LOG_ERROR_FUNC(class_name, error_message + " (标准异常): " + std::string(e.what()));
            return default_value;
        } catch (...) {
            LOG_ERROR_FUNC(class_name, error_message + " (未知异常)");
            return default_value;
        }
    }
};

} // namespace utils
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_UTILS_COMMON_UTILS_H
