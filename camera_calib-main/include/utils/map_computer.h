//
// Created by rang<PERSON><PERSON> on 19-6-18.
//

#ifndef CAMERA_CALIBRATION_UTILS_MAP_COMPUTER_H
#define CAMERA_CALIBRATION_UTILS_MAP_COMPUTER_H

#include <opencv2/opencv.hpp>

/**
 * @brief 自定义的标定映射表初始化函数，支持单应矩阵变换
 * 
 * 这个函数是对 OpenCV 标准 initUndistortRectifyMap 的扩展，
 * 增加了对单应矩阵（透视变换）的支持，用于前向标定。
 * 
 * @param _cameraMatrix 相机内参矩阵
 * @param _distCoeffs 畸变系数
 * @param _homoIMatrix 单应矩阵的逆（透视变换矩阵的逆）
 * @param _matR 旋转矩阵（通常为空）
 * @param _newCameraMatrix 新的相机矩阵
 * @param size 输出映射表的尺寸
 * @param m1type 映射表数据类型（CV_32FC1 或 CV_16SC2）
 * @param _map1 输出的 X 坐标映射表
 * @param _map2 输出的 Y 坐标映射表
 */
void initCalibRectifyMap(cv::InputArray _cameraMatrix, cv::InputArray _distCoeffs, cv::InputArray _homoIMatrix,
                         cv::InputArray _matR, cv::InputArray _newCameraMatrix,
                         cv::Size size, int m1type, cv::OutputArray _map1, cv::OutputArray _map2);

/**
 * @brief 计算倾斜投影矩阵
 * @param tauX X方向倾斜角度
 * @param tauY Y方向倾斜角度
 * @param matTilt 输出的倾斜投影矩阵
 */
void computeTiltProjectionMatrix(double tauX, double tauY, cv::Matx33d* matTilt);

#endif // CAMERA_CALIBRATION_UTILS_MAP_COMPUTER_H
