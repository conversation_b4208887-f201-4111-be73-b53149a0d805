/**
 * @file opencv_utils_impl.h
 * @brief OpenCV 工具类模板函数实现
 * <AUTHOR> Calibration Team
 * @date 2024
 */

#ifndef CAMERA_CALIBRATION_UTILS_OPENCV_UTILS_IMPL_H
#define CAMERA_CALIBRATION_UTILS_OPENCV_UTILS_IMPL_H

#include "common_utils.h"

namespace camera_calibration {
namespace utils {

template<typename Func>
bool OpenCVUtils::executeOpenCVOperation(Func&& operation,
                                        const std::string& operation_name,
                                        const std::string& class_name) {
    try {
        operation();
        return true;
    } catch (const cv::Exception& e) {
        LOG_ERROR_FUNC(class_name, "OpenCV操作失败 [" + operation_name + "]: " + std::string(e.what()));
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC(class_name, "操作异常 [" + operation_name + "]: " + std::string(e.what()));
        return false;
    } catch (...) {
        LOG_ERROR_FUNC(class_name, "未知异常 [" + operation_name + "]");
        return false;
    }
}

template<typename Func, typename ReturnType>
ReturnType OpenCVUtils::executeOpenCVOperation(Func&& operation,
                                              ReturnType default_value,
                                              const std::string& operation_name,
                                              const std::string& class_name) {
    try {
        return operation();
    } catch (const cv::Exception& e) {
        LOG_ERROR_FUNC(class_name, "OpenCV操作失败 [" + operation_name + "]: " + std::string(e.what()));
        return default_value;
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC(class_name, "操作异常 [" + operation_name + "]: " + std::string(e.what()));
        return default_value;
    } catch (...) {
        LOG_ERROR_FUNC(class_name, "未知异常 [" + operation_name + "]");
        return default_value;
    }
}

} // namespace utils
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_UTILS_OPENCV_UTILS_IMPL_H
