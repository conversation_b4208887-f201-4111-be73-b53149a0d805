#ifndef CAMERA_CALIBRATION_PROCESSING_IMAGE_ENHANCER_H
#define CAMERA_CALIBRATION_PROCESSING_IMAGE_ENHANCER_H

#include "core/types.h"
#include <opencv2/opencv.hpp>
#include <vector>
#include <string>

namespace camera_calibration {
namespace processing {

/**
 * @brief 图像增强器类
 * 
 * 负责对输入图像进行各种增强处理，包括：
 * - MSRCR (Multi-Scale Retinex with Color Restoration) 增强
 * - 地面区域检测和增强
 * - 黑色区域加深处理
 * - 形态学操作
 */
class ImageEnhancer {
public:
    /**
     * @brief 构造函数
     */
    ImageEnhancer();
    
    /**
     * @brief 析构函数
     */
    ~ImageEnhancer();
    
    /**
     * @brief 初始化图像增强器
     * @param processing_params 图像处理参数
     * @return 初始化是否成功
     */
    bool initialize(const core::ImageProcessingParams& processing_params);
    
    /**
     * @brief 执行完整的图像增强流程
     * @param input_image 输入图像
     * @param enhanced_image 输出增强后的图像
     * @return 增强是否成功
     */
    bool enhanceImage(const cv::Mat& input_image, cv::Mat& enhanced_image);
    
    /**
     * @brief MSRCR 多尺度视网膜增强
     * @param input_image 输入图像
     * @param enhanced_image 输出增强后的图像
     * @param weights 权重向量
     * @param sigmas 尺度参数向量
     * @param gain 增益参数
     * @param offset 偏移参数
     * @return 增强是否成功
     */
    bool applyMSRCR(const cv::Mat& input_image, cv::Mat& enhanced_image,
                   const std::vector<double>& weights = {0.1, 0.1, 0.1},
                   const std::vector<double>& sigmas = {30.0, 150.0, 300.0},
                   double gain = 128.0, double offset = 128.0,
                   double restoration_factor = 6.0, double color_gain = 2.0);
    
    /**
     * @brief 检测地面区域的起始行
     * @param image 输入图像
     * @return 地面起始行号，-1 表示未找到
     */
    int findGroundStartRow(const cv::Mat& image);
    
    /**
     * @brief 增强地面区域
     * @param input_image 输入图像
     * @param enhanced_image 输出增强后的图像
     * @param threshold 二值化阈值
     * @return 增强是否成功
     */
    bool enhanceGroundRegion(const cv::Mat& input_image, cv::Mat& enhanced_image, int ground_start_row, int threshold = 120);
    

    
    /**
     * @brief 应用形态学操作
     * @param input_image 输入图像
     * @param output_image 输出图像
     * @param operation 形态学操作类型 (MORPH_DILATE, MORPH_ERODE, MORPH_OPEN, MORPH_CLOSE)
     * @param kernel_size 核大小
     * @param iterations 迭代次数
     * @return 操作是否成功
     */
    bool applyMorphologyOperation(const cv::Mat& input_image, cv::Mat& output_image);
    

    
    /**
     * @brief 获取增强统计信息
     */
    struct EnhancementStats {
        bool ground_region_detected;
        int ground_start_row;
        double mean_intensity_before;
        double mean_intensity_after;
        double contrast_improvement;
        cv::Size processed_size;
        std::string status_message;
    };
    
    EnhancementStats getLastEnhancementStats() const { return last_stats_; }
    
    /**
     * @brief 保存中间处理结果
     * @param image 图像
     * @param filename 文件名
     * @param output_path 输出路径
     */
    void saveIntermediateResult(const cv::Mat& image, const std::string& filename,
                               const std::string& output_path);
    
    /**
     * @brief 设置调试模式
     * @param enable 是否启用调试模式
     */
    void setDebugMode(bool enable) { debug_mode_ = enable; }
    
    /**
     * @brief 获取调试模式状态
     */
    bool isDebugMode() const { return debug_mode_; }
    
private:

    
    // 验证图像有效性
    bool validateImage(const cv::Mat& image);
    

    
    // 成员变量
    bool initialized_;
    bool debug_mode_;
    
    // 处理参数
    core::ImageProcessingParams processing_params_;
    
    // 统计信息
    EnhancementStats last_stats_;
    
    // MSRCR 参数
    std::vector<double> default_weights_;
    std::vector<double> default_sigmas_;
    double default_gain_;
    double default_offset_;
    
    // 常量
    static const int MIN_GROUND_DETECTION_THRESHOLD;
    static const int DEFAULT_BORDER_WIDTH;
    static const double DEFAULT_SCALE_FACTOR;
};

} // namespace processing
} // namespace camera_calibration

#endif // CAMERA_CALIBRATION_PROCESSING_IMAGE_ENHANCER_H
