cmake_minimum_required(VERSION 3.10)
project(camera_calibration_refactored VERSION 1.0.0 LANGUAGES CXX)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 编译选项
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# 查找依赖包
find_package(OpenCV REQUIRED)
find_package(yaml-cpp REQUIRED)

# 检查 OpenCV 版本
if(OpenCV_VERSION VERSION_LESS "4.0")
    message(FATAL_ERROR "OpenCV version ${OpenCV_VERSION} is too old. Require OpenCV 4.0 or newer.")
endif()

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${OpenCV_INCLUDE_DIRS}
)

# 收集源文件
file(GLOB_RECURSE CORE_SOURCES "src/core/*.cpp")
file(GLOB_RECURSE PROCESSING_SOURCES "src/processing/*.cpp")
file(GLOB_RECURSE CALIBRATION_SOURCES "src/calibration/*.cpp")
file(GLOB_RECURSE UTILS_SOURCES "src/utils/*.cpp")

set(ALL_SOURCES
    ${CORE_SOURCES}
    ${PROCESSING_SOURCES}
    ${CALIBRATION_SOURCES}
    ${UTILS_SOURCES}
)

# 创建静态库
add_library(camera_calibration_lib STATIC ${ALL_SOURCES})

# 设置库的包含目录
target_include_directories(camera_calibration_lib PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${OpenCV_INCLUDE_DIRS}
)

# 链接库的依赖
target_link_libraries(camera_calibration_lib
    ${OpenCV_LIBS}
    yaml-cpp
)

# 创建主可执行文件
add_executable(camera_calibration src/main.cpp)

# 链接主程序
target_link_libraries(camera_calibration
    camera_calibration_lib
    ${OpenCV_LIBS}
    yaml-cpp
)

# 设置输出目录
set_target_properties(camera_calibration PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

set_target_properties(camera_calibration_lib PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

# 安装规则
install(TARGETS camera_calibration
    RUNTIME DESTINATION bin
)

install(TARGETS camera_calibration_lib
    ARCHIVE DESTINATION lib
)

install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)

# 测试支持
option(BUILD_TESTS "Build unit tests" OFF)
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(test)
endif()

# 文档生成
option(BUILD_DOCS "Build documentation" OFF)
if(BUILD_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        add_subdirectory(docs)
    endif()
endif()

# 打印配置信息
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "OpenCV version: ${OpenCV_VERSION}")
message(STATUS "OpenCV libraries: ${OpenCV_LIBS}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
