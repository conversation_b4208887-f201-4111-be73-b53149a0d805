#include "processing/msrcr.h"
#include <cmath>
#include <iostream>

namespace camera_calibration {
namespace processing {



void Msrcr::FastFilter(IplImage *img, double sigma)
{
    int filter_size;

    // Reject unreasonable demands
    // 设置上限
    if ( sigma > 300 ) sigma = 300;

    // get needed filter size (enforce oddness)
    // 获取需要的滤波尺寸，且强制为奇数；
    filter_size = (int)floor(sigma*6) / 2;
    filter_size = filter_size * 2 + 1;

    // If 3 sigma is less than a pixel, why bother (ie sigma < 2/3)
    // 如果3 * sigma小于一个像素，则直接退出
    if(filter_size < 3) return;

    // Filter, or downsample and recurse
    // 处理方式：(1) 滤波  (2) 高斯光滑处理  (3) 递归处理滤波器大小
    if (filter_size < 10) {

#ifdef USE_EXACT_SIGMA
        FilterGaussian(img, sigma);
#else
        cvSmooth( img, img, CV_GAUSSIAN, filter_size, filter_size );
#endif

    }
    else
    {
        if (img->width < 2 || img->height < 2) return;
        IplImage* sub_img = cvCreateImage(cvSize(img->width / 2, img->height / 2), img->depth, img->nChannels);
        cvPyrDown( img, sub_img );
        FastFilter( sub_img, sigma / 2.0 );
        cvResize( sub_img, img, CV_INTER_LINEAR );
        cvReleaseImage( &sub_img );
    }
}

void Msrcr::FastFilter(cv::Mat src, cv::Mat &dst, double sigma) {
    // 实现与原项目完全一致的 FastFilter 算法
    int filter_size;

    // 拒绝不合理的要求
    if (sigma > 300) sigma = 300;

    // 获取需要的滤波尺寸（强制为奇数）
    filter_size = static_cast<int>(std::floor(sigma * 6)) / 2;
    filter_size = filter_size * 2 + 1;

    // 如果 3*sigma 小于一个像素，则直接退出
    if (filter_size < 3) {
        dst = src.clone();
        return;
    }

    // 滤波或下采样并递归
    if (filter_size < 10) {
        // 直接使用高斯滤波
        cv::GaussianBlur(src, dst, cv::Size(filter_size, filter_size), 0, 0);
    } else {
        if (src.cols < 2 || src.rows < 2) {
            dst = src.clone();
            return;
        }

        // 使用图像金字塔
        cv::Mat sub_img;
        cv::pyrDown(src, sub_img);

        cv::Mat filtered_sub;
        FastFilter(sub_img, filtered_sub, sigma / 2.0);

        cv::resize(filtered_sub, dst, src.size(), 0, 0, cv::INTER_LINEAR);
    }
}


void Msrcr::MultiScaleRetinexCR(cv::Mat src, cv::Mat &dst,
                               std::vector<double> weights,
                               std::vector<double> sigmas,
                               int gain, int offset,
                               double restoration_factor, double color_gain) {
    // 1. 检查输入合法性
    if (src.empty()) {
        std::cerr << "输入图像为空！" << std::endl;
        dst = src.clone();
        return;
    }

    // 2. 将 Mat 转换为 IplImage* (与原项目完全一致的实现方式)
    IplImage src_ipl = cvIplImage(src);  // 注意，这里是结构体，不是指针

    // 3. 调用旧接口处理 (与原项目第787行完全一致)
    MultiScaleRetinexCR(&src_ipl, weights, sigmas, gain, offset, restoration_factor, color_gain);

    // 4. 将结果转换回 Mat（注意这里直接用 src 拷贝数据，与原项目第790行完全一致）
    dst = cv::cvarrToMat(&src_ipl, true);  // 深拷贝，避免悬空指针
}

void Msrcr::MultiScaleRetinexCR(IplImage *img, std::vector<double> weights, std::vector<double> sigmas,
                                int gain, int offset, double restoration_factor, double color_gain)
{
    int i;
    double weight;
    int scales = sigmas.size();
    IplImage *A, *B, *C, *fA, *fB, *fC, *fsA, *fsB, *fsC, *fsD, *fsE, *fsF;

    // Initialize temp images
    // 初始化缓存图像
    fA = cvCreateImage(cvSize(img->width, img->height), IPL_DEPTH_32F, img->nChannels);

    fB = cvCreateImage(cvSize(img->width, img->height), IPL_DEPTH_32F, img->nChannels);
    fC = cvCreateImage(cvSize(img->width, img->height), IPL_DEPTH_32F, img->nChannels);
    fsA = cvCreateImage(cvSize(img->width, img->height), IPL_DEPTH_32F, 1);
    fsB = cvCreateImage(cvSize(img->width, img->height), IPL_DEPTH_32F, 1);
    fsC = cvCreateImage(cvSize(img->width, img->height), IPL_DEPTH_32F, 1);
    fsD = cvCreateImage(cvSize(img->width, img->height), IPL_DEPTH_32F, 1);
    fsE = cvCreateImage(cvSize(img->width, img->height), IPL_DEPTH_32F, 1);
    fsF = cvCreateImage(cvSize(img->width, img->height), IPL_DEPTH_32F, 1);

    // Compute log image
    // 计算对数图像
    cvConvert( img, fB );
    cvLog( fB, fA );

    // Normalize according to given weights
    // 依照权重归一化
    for (i = 0, weight = 0; i < scales; i++)
        weight += weights[i];

    if (weight != 1.0) cvScale( fA, fA, weight );

    // Filter at each scale
    // 各尺度上进行滤波操作
    for (i = 0; i < scales; i++) {
        A = cvCloneImage( img );
        FastFilter( A, sigmas[i] );

        cvConvert( A, fB );
        cvLog( fB, fC );
        cvReleaseImage( &A );

        // Compute weighted difference
        // 计算权重后两图像之差
        cvScale( fC, fC, weights[i] );
        cvSub( fA, fC, fA );
    }

    // Color restoration
    // 颜色修复
    if (img->nChannels > 1) {
        A = cvCreateImage(cvSize(img->width, img->height), img->depth, 1);
        B = cvCreateImage(cvSize(img->width, img->height), img->depth, 1);
        C = cvCreateImage(cvSize(img->width, img->height), img->depth, 1);

        // Divide image into channels, convert and store sum
        // 将图像分割为若干通道，类型转换为浮点型，并存储通道数据之和
        cvSplit( img, A, B, C , NULL );
        cvConvert( A, fsA );
        cvConvert( B, fsB );
        cvConvert( C, fsC );

        cvReleaseImage( &A );
        cvReleaseImage( &B );
        cvReleaseImage( &C );

        // Sum components
        // 求和
        cvAdd( fsA, fsB, fsD );
        cvAdd( fsD, fsC, fsD );

        // Normalize weights
        // 带权重矩阵归一化
        cvDiv( fsA, fsD, fsA, restoration_factor);
        cvDiv( fsB, fsD, fsB, restoration_factor);
        cvDiv( fsC, fsD, fsC, restoration_factor);

        cvConvertScale( fsA, fsA, 1, 1 );
        cvConvertScale( fsB, fsB, 1, 1 );
        cvConvertScale( fsC, fsC, 1, 1 );

        // Log weights
        // 带权重矩阵求对数
        cvLog( fsA, fsA );
        cvLog( fsB, fsB );
        cvLog( fsC, fsC );

        // Divide retinex image, weight accordingly and recombine
        // 将Retinex图像切分为三个数组，按照权重和颜色增益重新组合
        cvSplit( fA, fsD, fsE, fsF, NULL );

        cvMul( fsD, fsA, fsD, color_gain);
        cvMul( fsE, fsB, fsE, color_gain );
        cvMul( fsF, fsC, fsF, color_gain );

        cvMerge( fsD, fsE, fsF, NULL, fA );
    }

    // Restore
    // 恢复图像
    cvConvertScale( fA, img, gain, offset);

    // Release temp images
    // 释放缓存图像
    cvReleaseImage( &fA );
    cvReleaseImage( &fB );
    cvReleaseImage( &fC );
    cvReleaseImage( &fsA );
    cvReleaseImage( &fsB );
    cvReleaseImage( &fsC );
    cvReleaseImage( &fsD );
    cvReleaseImage( &fsE );
    cvReleaseImage( &fsF );
}

} // namespace processing
} // namespace camera_calibration
