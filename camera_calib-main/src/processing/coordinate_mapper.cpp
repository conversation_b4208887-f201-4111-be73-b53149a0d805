#include "processing/coordinate_mapper.h"
#include "utils/common_utils.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <cmath>
#include <algorithm>

namespace camera_calibration {
namespace processing {

CoordinateMapper::CoordinateMapper()
    : initialized_(false)
    , debug_mode_(false)
    , cols_(14)
    , rows_(7)
    , distance_to_camera_(1.0)
    , distance_to_camera_center_(0.0) {
}

CoordinateMapper::~CoordinateMapper() {
    // 清理资源
}

bool CoordinateMapper::initialize(const core::ImageProcessingParams& processing_params) {
    try {
        processing_params_ = processing_params;
        
        // 初始化世界坐标网格
        initializeWorldGrid();
        
        // 初始化像素坐标网格
        initializePixelGrid();
        
        initialized_ = true;
        LOG_INFO_FUNC("CoordinateMapper", "坐标映射器初始化成功");
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CoordinateMapper", "坐标映射器初始化失败: " + std::string(e.what()));
        return false;
    }
}



core::CalibrationResult CoordinateMapper::mapPixelToWorld(const core::Point2D& pixel_point) {
    core::CalibrationResult result;
    
    // 初始化结果为无效值
    result.world_position = core::Point3D(-1, -1, -1);
    result.pixel_position = pixel_point;
    result.confidence = 0.0;
    
    if (!initialized_) {
        LOG_ERROR_FUNC("CoordinateMapper", "坐标映射器未初始化");
        return result;
    }
    
    try {
        // 遍历所有网格单元，寻找包含目标像素点的单元
        for (int i = 0; i < rows_ - 1; ++i) {
            for (int j = 0; j < cols_ - 1; ++j) {
                
                // 跳过某些特殊单元（参考原项目逻辑）
                if ((i == 1 && j == 12) || (i == 2 && j == 11) || 
                    (i == 3 && j == 9) || (i == 4 && j == 8)) {
                    continue;
                }
                
                // 获取四个角点
                PointInfo left_top = pixels_3d_[i][j];
                PointInfo right_bottom = pixels_3d_[i + 1][j + 1];
                PointInfo left_bottom = pixels_3d_[i + 1][j];
                PointInfo right_top = pixels_3d_[i][j + 1];
                
                // 检查角点是否有效
                if (left_top.x == 0 || left_top.y == 0 || 
                    right_bottom.x == 0 || right_bottom.y == 0) {
                    continue;
                }
                
                // 获取对应的世界坐标
                PointInfo left_top_pose = poses_3d_[i][j];
                PointInfo right_bottom_pose = poses_3d_[i + 1][j + 1];
                PointInfo left_bottom_pose = poses_3d_[i + 1][j];
                PointInfo right_top_pose = poses_3d_[i][j + 1];
                
                // 尝试三角形插值（左下三角形）
                if (left_bottom.x != 0 && left_bottom.y != 0) {
                    if (isPointInTriangle(pixel_point, left_top, left_bottom, right_bottom)) {
                        core::Point3D world_pos = triangularInterpolation(
                            pixel_point, 
                            left_top, left_bottom, right_bottom,
                            left_top_pose, left_bottom_pose, right_bottom_pose
                        );
                        
                        result.world_position = world_pos;
                        result.left_top = core::Point2D(left_top.x, left_top.y);
                        result.left_bottom = core::Point2D(left_bottom.x, left_bottom.y);
                        result.right_bottom = core::Point2D(right_bottom.x, right_bottom.y);
                        result.right_top = core::Point2D(right_bottom.x, right_bottom.y);
                        result.confidence = 0.8;
                        
                        return result;
                    }
                }
                
                // 尝试三角形插值（右上三角形）
                if (right_top.x != 0 && right_top.y != 0) {
                    if (isPointInTriangle(pixel_point, left_top, right_top, right_bottom)) {
                        core::Point3D world_pos = triangularInterpolation(
                            pixel_point,
                            left_top, right_top, right_bottom,
                            left_top_pose, right_top_pose, right_bottom_pose
                        );
                        
                        result.world_position = world_pos;
                        result.left_top = core::Point2D(left_top.x, left_top.y);
                        result.right_top = core::Point2D(right_top.x, right_top.y);
                        result.right_bottom = core::Point2D(right_bottom.x, right_bottom.y);
                        result.left_bottom = core::Point2D(left_top.x, left_top.y);
                        result.confidence = 0.8;
                        
                        return result;
                    }
                }
            }
        }
        
        // 如果没有找到合适的三角形，尝试最近邻插值
        result = nearestNeighborInterpolation(pixel_point);
        
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CoordinateMapper", "像素到世界坐标映射异常: " + std::string(e.what()));
    }
    
    return result;
}


bool CoordinateMapper::saveCalibrationResults(const std::vector<core::FeaturePoint>& feature_points,
                                             const std::string& output_file) {
    try {
        char separator = ',';
        std::ofstream output(output_file);
        if (!output.is_open()) {
            LOG_ERROR_FUNC("CoordinateMapper", "无法创建输出文件: " + output_file);
            return false;
        }

        // 与原项目完全一致的输出方式
        for (size_t i = 0; i < feature_points.size(); i++) {
            // 与原项目保持一致：将像素坐标转换为整数后再除以2
            int pixel_x = static_cast<int>(feature_points[i].pixel_coord.x);
            int pixel_y = static_cast<int>(feature_points[i].pixel_coord.y);
            output << feature_points[i].label.x << separator << feature_points[i].label.y << separator
                << pixel_x/2 << separator << pixel_y/2 << separator << std::endl;
        }

        output.close();
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CoordinateMapper", "保存标定结果异常: " + std::string(e.what()));
        return false;
    }
}

// 私有方法实现
void CoordinateMapper::initializeWorldGrid() {
    // 初始化世界坐标轴（参考原项目的配置）
    h_axis_ = {80, 60, 45, 35, 25, 15, 0}; // Z 方向距离
    w_axis_ = {-32.5, -27.5, -22.5, -17.5, -12.5, -7.5, -2.5,
               2.5, 7.5, 12.5, 17.5, 22.5, 27.5, 32.5}; // Y 方向距离
    
    // 初始化世界坐标网格
    poses_3d_.resize(rows_);
    for (int i = 0; i < rows_; ++i) {
        poses_3d_[i].resize(cols_);
        for (int j = 0; j < cols_; ++j) {
            poses_3d_[i][j].x = 0.0;
            poses_3d_[i][j].y = 0.0;
        }
    }
}

void CoordinateMapper::initializePixelGrid() {
    // 初始化像素坐标网格
    pixels_3d_.resize(rows_);
    for (int i = 0; i < rows_; ++i) {
        pixels_3d_[i].resize(cols_);
        for (int j = 0; j < cols_; ++j) {
            pixels_3d_[i][j].x = 0.0;
            pixels_3d_[i][j].y = 0.0;
        }
    }
}

bool CoordinateMapper::isPointInTriangle(const core::Point2D& point,
                                        const PointInfo& p1, const PointInfo& p2, const PointInfo& p3) {
    // 使用叉积判断点是否在三角形内
    double v1 = crossProduct(p1, p2, point);
    double v2 = crossProduct(p2, p3, point);
    double v3 = crossProduct(p3, p1, point);
    
    // 检查所有叉积的符号是否一致
    return (v1 <= 0 && v2 <= 0 && v3 <= 0) || (v1 >= 0 && v2 >= 0 && v3 >= 0);
}

core::Point3D CoordinateMapper::triangularInterpolation(const core::Point2D& point,
                                                       const PointInfo& p1, const PointInfo& p2, const PointInfo& p3,
                                                       const PointInfo& w1, const PointInfo& w2, const PointInfo& w3) {
    // 计算三角形面积
    double area1 = calculateTriangleArea(p1, p2, point);
    double area2 = calculateTriangleArea(p2, p3, point);
    double area3 = calculateTriangleArea(p3, p1, point);
    double total_area = area1 + area2 + area3;
    
    if (total_area == 0.0) {
        return core::Point3D(-1, -1, -1);
    }
    
    // 重心坐标插值
    double x = (w3.x * area1 + w1.x * area2 + w2.x * area3) / total_area;
    double y = (w3.y * area1 + w1.y * area2 + w2.y * area3) / total_area;
    
    return core::Point3D(x, y, 0.0);
}

core::CalibrationResult CoordinateMapper::nearestNeighborInterpolation(const core::Point2D& pixel_point) {
    core::CalibrationResult result;
    result.world_position = core::Point3D(-1, -1, -1);
    result.pixel_position = pixel_point;
    result.confidence = 0.0;
    
    double min_distance = std::numeric_limits<double>::max();
    int best_i = -1, best_j = -1;
    
    // 寻找最近的有效像素点
    for (int i = 0; i < rows_; ++i) {
        for (int j = 0; j < cols_; ++j) {
            if (pixels_3d_[i][j].x != 0 || pixels_3d_[i][j].y != 0) {
                double dx = pixel_point.x - pixels_3d_[i][j].x;
                double dy = pixel_point.y - pixels_3d_[i][j].y;
                double distance = std::sqrt(dx * dx + dy * dy);
                
                if (distance < min_distance) {
                    min_distance = distance;
                    best_i = i;
                    best_j = j;
                }
            }
        }
    }
    
    if (best_i >= 0 && best_j >= 0) {
        result.world_position = core::Point3D(poses_3d_[best_i][best_j].x, 
                                             poses_3d_[best_i][best_j].y, 0.0);
        result.confidence = std::max(0.0, 1.0 - min_distance / 100.0); // 距离越近置信度越高
    }
    
    return result;
}

double CoordinateMapper::crossProduct(const PointInfo& p1, const PointInfo& p2, const core::Point2D& p3) {
    double dx1 = p2.x - p1.x;
    double dy1 = p2.y - p1.y;
    double dx2 = p3.x - p1.x;
    double dy2 = p3.y - p1.y;
    
    return dx1 * dy2 - dy1 * dx2;
}

double CoordinateMapper::calculateTriangleArea(const PointInfo& p1, const PointInfo& p2, const core::Point2D& p3) {
    return 0.5 * std::abs(p1.x * p2.y + p2.x * p3.x + p3.x * p1.y - 
                         p1.x * p3.y - p2.x * p1.y - p3.x * p2.y);
}



// 与原项目完全一致的查找表生成第二部分：插值处理和二进制输出
bool CoordinateMapper::generateLookupTableWithInterpolation(float* loc, int table_height, int table_width,
                                                           int IMGX_RANGE[2], int IMGY_RANGE[2],
                                                           std::ofstream& file, std::ofstream& log) {
    try {
        // 第二步：插值处理（与原项目完全一致）
        for (int i = IMGY_RANGE[0]; i <= IMGY_RANGE[1]; i++) {
            int col1 = findNonZero3Original(loc, i, table_width, IMGX_RANGE, IMGY_RANGE);  // 找每一行的左右非零列
            int col2 = findNonZero4Original(loc, i, table_width, IMGX_RANGE, IMGY_RANGE);

            if (col2 - col1 > 500) {
                bool colConnect = checkColumnConnectivityOriginal(loc, i, col1, col2, table_width, IMGX_RANGE, IMGY_RANGE);

                if (colConnect) {
                    // 左半部分插值（与原项目完全一致）
                    for (int j = IMGX_RANGE[0]; j < IMGX_RANGE[1]/2; j++) {
                        if (loc[2 * ((i - IMGY_RANGE[0]) * table_width + (j - IMGX_RANGE[0]))] == 0) {
                            // Y坐标插值（与原项目公式完全一致）
                            loc[2 * ((i - IMGY_RANGE[0]) * table_width + j - IMGX_RANGE[0]) + 1] =
                                (loc[2 * ((i - IMGY_RANGE[0]) * table_width + (col1 - IMGX_RANGE[0])) + 1] -
                                 loc[2 * ((i - IMGY_RANGE[0]) * table_width + (col2 - IMGX_RANGE[0])) + 1]) /
                                (col2 - col1) * (col2 - j) +
                                loc[2 * ((i - IMGY_RANGE[0]) * table_width + (col2 - IMGX_RANGE[0])) + 1];
                            // X坐标复制
                            loc[2 * ((i - IMGY_RANGE[0]) * table_width + j - IMGX_RANGE[0])] =
                                loc[2 * ((i - IMGY_RANGE[0]) * table_width + (col1 - IMGX_RANGE[0]))];
                        }
                    }

                    // 右半部分插值（与原项目完全一致）
                    for (int j = IMGX_RANGE[1]/2; j <= IMGX_RANGE[1]; j++) {
                        if (loc[2 * ((i - IMGY_RANGE[0]) * table_width + (j - IMGX_RANGE[0]))] == 0) {
                            // Y坐标插值（与原项目公式完全一致）
                            loc[2 * ((i - IMGY_RANGE[0]) * table_width + j - IMGX_RANGE[0]) + 1] =
                                loc[2 * ((i - IMGY_RANGE[0]) * table_width + (col1 - IMGX_RANGE[0])) + 1] -
                                ((loc[2 * ((i - IMGY_RANGE[0]) * table_width + (col1 - IMGX_RANGE[0])) + 1] -
                                  loc[2 * ((i - IMGY_RANGE[0]) * table_width + (col2 - IMGX_RANGE[0])) + 1]) /
                                 (col2 - col1) * (j - col1));
                            // X坐标复制
                            loc[2 * ((i - IMGY_RANGE[0]) * table_width + j - IMGX_RANGE[0])] =
                                loc[2 * ((i - IMGY_RANGE[0]) * table_width + (col2 - IMGX_RANGE[0]))];
                        }
                    }
                }
            }
        }

        // 第三步：输出调试信息到文本文件（与原项目完全一致的时机）
        std::ofstream debug_file("123.txt");
        for (int i = 0; i < table_height * 2 * table_width; ++i) {
            debug_file << loc[i] << " ";
        }
        debug_file.close();

        // 第四步：量化并写入二进制文件（与原项目完全一致）
        unsigned char* loc_xy = new unsigned char[2 * table_width * table_height];
        for (int i = 0; i < 2 * table_height * table_width; i += 2) {
            int nx = calcLocOriginal(loc[i], static_cast<int>(processing_params_.world_x_min), static_cast<int>(processing_params_.world_x_max));
            int ny = calcLocOriginal(loc[i + 1], static_cast<int>(processing_params_.world_y_min), static_cast<int>(processing_params_.world_y_max));
            loc_xy[i] = static_cast<uint8_t>(nx);
            loc_xy[i + 1] = static_cast<uint8_t>(ny);
            file << loc_xy[i] << loc_xy[i + 1];
        }

        file.close();
        log.close();

        std::cout << ">>>Done!" << std::endl;
        std::cout << ">>>Number of Points Processed: " << 2 * table_height * table_width << std::endl;

        delete[] loc;
        delete[] loc_xy;
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR_FUNC("CoordinateMapper", "查找表插值处理异常: " + std::string(e.what()));
        delete[] loc;
        return false;
    }
}

// 与原项目完全一致的辅助函数实现
int CoordinateMapper::findNonZero3Original(float* loc_temp, int pixel_row, int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2]) {
    int noZeroCol1 = -1;
    for (int i = IMGX_RANGE[1]/2; i >= IMGX_RANGE[0]; i--) {
        float temp = loc_temp[2 * ((pixel_row - IMGY_RANGE[0]) * table_width + (i - IMGX_RANGE[0]))];
        if (temp == 0) {
            noZeroCol1 = i + 1;
            break;
        }
    }
    return noZeroCol1;
}

int CoordinateMapper::findNonZero4Original(float* loc_temp, int pixel_row, int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2]) {
    int noZeroCol2 = -1;
    for (int i = IMGX_RANGE[1]/2; i <= IMGX_RANGE[1]; i++) {
        float temp = loc_temp[2 * ((pixel_row - IMGY_RANGE[0]) * table_width + (i - IMGX_RANGE[0]))];
        if (temp == 0) {
            noZeroCol2 = i - 1;
            break;
        }
    }
    return noZeroCol2;
}

bool CoordinateMapper::checkColumnConnectivityOriginal(float* loc_temp, int row, int col1, int col2, int table_width, int IMGX_RANGE[2], int IMGY_RANGE[2]) {
    bool colConnect = false;
    for (int j = col1; j <= col2; ++j) {
        float temp = loc_temp[2 * ((row - IMGY_RANGE[0]) * table_width + (j - IMGX_RANGE[0]))];
        if (temp == 0) {
            colConnect = false;
            return colConnect;
        } else {
            colConnect = true;
        }
    }
    return colConnect;
}

int CoordinateMapper::calcLocOriginal(double loc, int s_min, int s_max) {
    int loc_temp;
    int int_loc = static_cast<int>((loc - s_min) / (s_max - s_min) * 255 + 0.5);
    if (int_loc < 0) {
        loc_temp = 0;
    } else if (int_loc > 255) {
        loc_temp = 255;
    } else {
        loc_temp = int_loc;
    }
    return loc_temp;
}

} // namespace processing
} // namespace camera_calibration
