#include "utils/file_utils.h"
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>
#include <cstring>
#include <algorithm>

namespace camera_calibration {
namespace utils {

bool FileUtils::exists(const std::string& path) {
    struct stat buffer;
    return (stat(path.c_str(), &buffer) == 0);
}

bool FileUtils::createDirectories(const std::string& path) {
    if (path.empty()) {
        return false;
    }
    
    if (exists(path)) {
        return isDirectory(path);
    }
    // 递归创建父目录
    std::string parent = getDirectory(path);
    if (!parent.empty() && parent != path) {
        if (!createDirectories(parent)) {
            return false;
        }
    }
    
    // 创建当前目录
    return (mkdir(path.c_str(), 0755) == 0);
}

bool FileUtils::isDirectory(const std::string& path) {
    struct stat buffer;
    if (stat(path.c_str(), &buffer) != 0) {
        return false;
    }
    return S_ISDIR(buffer.st_mode);
}

bool FileUtils::isFile(const std::string& path) {
    struct stat buffer;
    if (stat(path.c_str(), &buffer) != 0) {
        return false;
    }
    return S_ISREG(buffer.st_mode);
}

long FileUtils::getFileSize(const std::string& path) {
    struct stat buffer;
    if (stat(path.c_str(), &buffer) != 0) {
        return -1;
    }
    return buffer.st_size;
}

std::string FileUtils::getDirectory(const std::string& path) {
    size_t pos = path.find_last_of("/\\");
    if (pos == std::string::npos) {
        return "";
    }
    return path.substr(0, pos);
}

std::string FileUtils::getFileName(const std::string& path) {
    size_t pos = path.find_last_of("/\\");
    if (pos == std::string::npos) {
        return path;
    }
    return path.substr(pos + 1);
}

std::string FileUtils::getExtension(const std::string& path) {
    std::string filename = getFileName(path);
    size_t pos = filename.find_last_of('.');
    if (pos == std::string::npos) {
        return "";
    }
    return filename.substr(pos);
}

std::string FileUtils::joinPath(const std::string& path1, const std::string& path2) {
    if (path1.empty()) {
        return path2;
    }
    if (path2.empty()) {
        return path1;
    }
    
    std::string result = path1;
    if (result.back() != '/' && result.back() != '\\') {
        result += '/';
    }
    
    std::string second = path2;
    if (second.front() == '/' || second.front() == '\\') {
        second = second.substr(1);
    }
    
    return result + second;
}

std::string FileUtils::normalizePath(const std::string& path) {
    // 简化的路径规范化实现
    std::string result = path;
    
    // 将反斜杠转换为正斜杠
    std::replace(result.begin(), result.end(), '\\', '/');
    
    // 移除重复的斜杠
    size_t pos = 0;
    while ((pos = result.find("//", pos)) != std::string::npos) {
        result.replace(pos, 2, "/");
    }
    
    return result;
}

} // namespace utils
} // namespace camera_calibration
