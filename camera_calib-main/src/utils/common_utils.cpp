#include "utils/common_utils.h"
#include "utils/file_utils.h"
#include <iostream>

namespace camera_calibration {
namespace utils {

// ImageValidator 实现
bool ImageValidator::validateImage(const cv::Mat& image, const std::string& class_name) {
    if (image.empty()) {
        LOG_ERROR_FUNC(class_name, "图像为空");
        return false;
    }
    
    if (image.cols <= 0 || image.rows <= 0) {
        LOG_ERROR_FUNC(class_name, "图像尺寸无效: " + std::to_string(image.cols) + "x" + std::to_string(image.rows));
        return false;
    }
    
    return true;
}

bool ImageValidator::validateImageSize(const cv::Mat& image, int min_width, int min_height, 
                                      const std::string& class_name) {
    if (!validateImage(image, class_name)) {
        return false;
    }
    
    if (image.cols < min_width || image.rows < min_height) {
        LOG_ERROR_FUNC(class_name, "图像尺寸过小: " + std::to_string(image.cols) + "x" + std::to_string(image.rows) +
                      ", 最小要求: " + std::to_string(min_width) + "x" + std::to_string(min_height));
        return false;
    }
    
    return true;
}

bool ImageValidator::validateImageChannels(const cv::Mat& image, int expected_channels,
                                          const std::string& class_name) {
    if (!validateImage(image, class_name)) {
        return false;
    }
    
    if (image.channels() != expected_channels) {
        LOG_ERROR_FUNC(class_name, "图像通道数不匹配: 期望 " + std::to_string(expected_channels) +
                      ", 实际 " + std::to_string(image.channels()));
        return false;
    }
    
    return true;
}

// FileOperationHelper 实现
bool FileOperationHelper::saveImageSafely(const cv::Mat& image, const std::string& filename,
                                          const std::string& output_path, 
                                          const std::string& class_name) {
    if (!ImageValidator::validateImage(image, class_name)) {
        return false;
    }
    
    try {
        // 创建输出目录
        if (!createOutputDirectory(output_path, class_name)) {
            return false;
        }
        
        // 构建完整路径
        std::string full_path = output_path;
        if (!output_path.empty() && output_path.back() != '/') {
            full_path += "/";
        }
        full_path += filename;
        
        // 保存图像
        if (!cv::imwrite(full_path, image)) {
            LOG_ERROR_FUNC(class_name, "保存图像失败: " + full_path);
            return false;
        }
        
        LOG_INFO_FUNC(class_name, "图像已保存: " + full_path);
        return true;
        
    } catch (const cv::Exception& e) {
        LOG_ERROR_FUNC(class_name, "保存图像时OpenCV异常: " + std::string(e.what()));
        return false;
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC(class_name, "保存图像时异常: " + std::string(e.what()));
        return false;
    }
}

bool FileOperationHelper::createOutputDirectory(const std::string& output_path,
                                                const std::string& class_name) {
    if (output_path.empty()) {
        return true; // 空路径认为是当前目录，不需要创建
    }
    
    try {
        if (!FileUtils::exists(output_path)) {
            if (!FileUtils::createDirectories(output_path)) {
                LOG_INFO_FUNC(class_name, "创建输出目录: " + output_path);
            }
        }
        return true;
        
    } catch (const std::exception& e) {
        LOG_ERROR_FUNC(class_name, "创建输出目录异常: " + std::string(e.what()));
        return false;
    }
}

} // namespace utils
} // namespace camera_calibration
