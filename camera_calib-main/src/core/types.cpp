#include "core/types.h"
#include <opencv2/features2d.hpp>

namespace camera_calibration {
namespace core {

cv::SimpleBlobDetector::Params BlobDetectorParams::toOpenCVParams() const {
    cv::SimpleBlobDetector::Params params;
    
    // 颜色过滤
    params.filterByColor = filter_by_color;
    params.blobColor = blob_color;
    
    // 面积过滤
    params.filterByArea = filter_by_area;
    params.minArea = min_area;
    params.maxArea = max_area;
    
    // 圆度过滤
    params.filterByCircularity = filter_by_circularity;
    params.minCircularity = min_circularity;
    params.maxCircularity = max_circularity;
    
    // 凸性过滤
    params.filterByConvexity = filter_by_convexity;
    params.minConvexity = min_convexity;
    params.maxConvexity = max_convexity;
    
    // 惯性比过滤
    params.filterByInertia = filter_by_inertia;
    params.minInertiaRatio = min_inertia_ratio;
    params.maxInertiaRatio = max_inertia_ratio;
    
    return params;
}

std::string errorCodeToString(ErrorCode code) {
    switch (code) {
        case ErrorCode::SUCCESS:
            return "成功";
        case ErrorCode::FILE_NOT_FOUND:
            return "文件未找到";
        case ErrorCode::INVALID_CONFIG:
            return "配置无效";
        case ErrorCode::IMAGE_LOAD_FAILED:
            return "图像加载失败";
        case ErrorCode::CALIBRATION_FAILED:
            return "标定失败";
        case ErrorCode::FORWARD_CALIB_FAILED:
            return "前向标定失败";
        case ErrorCode::FEATURE_DETECTION_FAILED:
            return "特征检测失败";
        case ErrorCode::INSUFFICIENT_POINTS:
            return "特征点数量不足";
        case ErrorCode::INVALID_PARAMETERS:
            return "参数无效";
        default:
            return "未知错误";
    }
}

} // namespace core
} // namespace camera_calibration
